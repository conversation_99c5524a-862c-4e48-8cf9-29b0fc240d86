<?php

namespace App\Http\Controllers;

use App\Http\Requests\ServiceRequest;
use App\Models\Category;
use App\Models\Staff;
use App\Services\UserService;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    public $userService;
    function __construct()
    {
        $this->userService = new UserService();
    }

    function index(Request $request)
    {
        $activeTab = $request->get('tab', 'services');

        if (auth()->user()->hasRole('admin')) {
            if ($activeTab === 'classes') {
                $individual_services = collect(); // Empty collection
                $group_services = $this->userService->getServices(type: 'group', withRelations: true);
            } else {
                $individual_services = $this->userService->getServices(type: 'individual', withRelations: true);
                $group_services = collect(); // Empty collection
            }
        } else {
            if ($activeTab === 'classes') {
                $individual_services = collect(); // Empty collection
                $group_services = $this->userService->getServices(userId: auth()->id(), type: 'group', withRelations: true);
            } else {
                $individual_services = $this->userService->getServices(userId: auth()->id(), type: 'individual', withRelations: true);
                $group_services = collect(); // Empty collection
            }
        }
        // Get categories for filter dropdown
        $categories = Category::active()->get();

        return view('dashboard.service.index', compact('individual_services', 'group_services', 'categories'));
    }

    public function filter(Request $request)
    {
        $tab = $request->get('tab', 'services');
        $search = $request->filled('search') ? trim($request->get('search')) : '';
        $categoryId = $request->filled('category') && $request->get('category') !== 'all' ? $request->get('category') : '';

        // Determine service type based on tab
        $type = $tab === 'classes' ? 'group' : 'individual';

        // Build query
        $query = \App\Models\Service::with(['category', 'subcategory', 'staff', 'availabilities']);

        // Apply user filter if not admin
        if (!auth()->user()->hasRole('admin')) {
            $query->where('user_id', auth()->id());
        }

        // Apply type filter
        $query->where('type', $type);

        // Apply search filter
        if (!empty($search)) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhereHas('category', function ($categoryQuery) use ($search) {
                      $categoryQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Apply category filter
        if (!empty($categoryId)) {
            $query->where('category_id', $categoryId);
        }

        $services = $query->get();

        // Return appropriate view based on tab
        if ($tab === 'classes') {
            $html = view('dashboard.service.partials.classes-table', ['group_services' => $services])->render();
        } else {
            $html = view('dashboard.service.partials.services-table', ['individual_services' => $services])->render();
        }

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $services->count()
        ]);
    }

    function create($type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $categories = Category::active()->get();
        $staffs = Staff::all();
        return view('dashboard.service.create', compact('type', 'categories', 'staffs'));
    }

    function store(ServiceRequest $request, $type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $service = $this->userService->createService(request_data: $request, type: $type);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service creation failed', 'title' => 'Error']);
        }
        return redirect()->route('services.index')->with(['type' => 'success', 'message' => 'Service created successfully', 'title' => 'Created']);
    }

    function edit($ids, $type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $service = $this->userService->getSingleService(service_ids: $ids);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service not found', 'title' => 'Error']);
        }
        $categories = Category::active()->get();
        $staffs = Staff::where('user_id', auth()->id())->get();
        return view('dashboard.service.edit', compact('service', 'type', 'categories', 'staffs'));
    }

    function update(ServiceRequest $request, $ids, $type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $service = $this->userService->updateService(request_data: $request, service_ids: $ids, type: $type);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service update failed', 'title' => 'Error']);
        }
        return redirect()->route('services.index')->with(['type' => 'success', 'message' => 'Service updated successfully', 'title' => 'Updated']);
    }

    public function getSubcategories($categoryId)
    {
        try {
            $category = Category::where('ids', $categoryId)->first();
            if (!$category) {
                return response()->json(['success' => false, 'message' => 'Category not found']);
            }

            $subcategories = $category->subcategories()->where('status', 1)->get(['ids', 'name']);
            return response()->json(['success' => true, 'subcategories' => $subcategories]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error fetching subcategories']);
        }
    }

    function destroy($ids)
    {
        $del_service = $this->userService->deleteService(service_ids: $ids);
        if (!$del_service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service deletion failed', 'title' => 'Error']);
        }
        return redirect()->back()->with(['type' => 'success', 'message' => 'Service deleted successfully', 'title' => 'Deleted']);
    }

    function show($ids)
    {
        $service = $this->userService->getSingleService(service_ids: $ids);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service not found', 'title' => 'Error']);
        }
        return view('dashboard.service.show', compact('service'));
    }
}
