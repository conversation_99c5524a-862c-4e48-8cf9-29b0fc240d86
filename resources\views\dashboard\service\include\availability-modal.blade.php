{{-- availability modal --}}
<style>
.service-availibility-calendar .modal-dialog {
    max-width: 500px;
}
.service-availibility-calendar .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
}
.service-availibility-calendar .modal-header {
    border-bottom: none;
    padding: 24px 24px 0;
}
.service-availibility-calendar .modal-body {
    padding: 20px 24px;
}
.service-availibility-calendar .modal-footer {
    border-top: none;
    padding: 0 24px 24px;
    justify-content: space-between;
}
.service-availibility-calendar .day-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}
.service-availibility-calendar .day-row .day-label {
    font-weight: 500;
    color: #333;
    margin: 0;
    flex: 1;
}
.service-availibility-calendar .day-row .day-status {
    color: #6c757d;
    font-size: 14px;
}
.service-availibility-calendar .form-check-input {
    margin-right: 12px;
    margin-top: 0;
}
.service-availibility-calendar .btn-link {
    color: #6c757d;
    text-decoration: none;
}
.service-availibility-calendar .btn-link:hover {
    color: #495057;
}
.service-availibility-calendar .time-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}
.service-availibility-calendar .time-inputs input {
    width: 80px;
    font-size: 12px;
    padding: 4px 8px;
}
.service-availibility-calendar #recurringOptions {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;
}
.service-availibility-calendar #customWeeksInput input {
    border-radius: 6px;
    border: 1px solid #ced4da;
}
</style>

<div class="modal fade service-availibility-calendar" id="availabilityModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content p-3">
            <div class="modal-header">
                <h5 class="modal-title">Availability</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="availability-calendar">
                    <!-- Week Navigation -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <button type="button" class="btn btn-link p-0" id="prevWeek">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <h6 class="mb-0 fw-bold" id="weekRange">11 Aug 2025 - 17 Aug 2025</h6>
                        <button type="button" class="btn btn-link p-0" id="nextWeek">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <!-- Days Container -->
                    <div id="weekDaysContainer" class="mb-4">
                        <!-- Default days structure -->
                        <div class="day-row" data-day="Monday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Monday">
                                <label class="day-label">Monday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Tuesday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Tuesday">
                                <label class="day-label">Tuesday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Wednesday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Wednesday">
                                <label class="day-label">Wednesday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Thursday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Thursday">
                                <label class="day-label">Thursday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Friday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Friday">
                                <label class="day-label">Friday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Saturday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Saturday">
                                <label class="day-label">Saturday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Sunday" data-date="">
                            <div class="d-flex align-items-center">
                                <input type="checkbox" class="form-check-input day-checkbox" data-day="Sunday">
                                <label class="day-label">Sunday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                    </div>

                    <!-- Recurring Checkbox -->
                    <div class="mb-4">
                        <label class="form-check">
                            <input class="form-check-input" type="checkbox" id="recurringCheckbox">
                            <span class="form-check-label">Recurring</span>
                        </label>
                    </div>

                    <!-- Recurring Options (Hidden by default) -->
                    <div id="recurringOptions" class="mb-4" style="display: none;">
                        <div class="ms-4">
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="recurringWeeks" id="recurring4weeks" value="4">
                                <label class="form-check-label" for="recurring4weeks">
                                    4 weeks
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="recurringWeeks" id="recurring8weeks" value="8">
                                <label class="form-check-label" for="recurring8weeks">
                                    8 weeks
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="recurringWeeks" id="recurringCustom" value="custom">
                                <label class="form-check-label" for="recurringCustom">
                                    Custom
                                </label>
                            </div>
                            <div id="customWeeksInput" class="mt-2" style="display: none;">
                                <div class="d-flex align-items-center gap-2">
                                    <input type="number" class="form-control form-control-sm" id="customWeeksNumber" placeholder="Enter weeks" min="1" max="52" style="width: 150px;">
                                    <button type="button" class="btn btn-primary btn-sm" id="applyCustomWeeks">Apply</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- JSON Output (Hidden) -->
                    <textarea id="jsonOutput" style="display: none;"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelAvailability">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveAvailability">Done</button>
            </div>
        </div>
    </div>
</div>

@push('js')
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// Wait for jQuery and moment.js to be available
$(document).ready(function() {
    // Check if moment.js is available
    if (typeof moment === 'undefined') {
        console.error('Moment.js is not loaded');
        return;
    }

    // Wrap everything in IIFE to prevent variable conflicts
    (function() {
        const weekData = {};
        let currentWeekIndex = 0;
        const baseStartDate = moment().startOf('isoWeek'); // This will set the start date to the current week's Monday

    // 🎯 JSON DATA INITIALIZATION - Pass your array JSON data here
    const initializeDataFromJSON = () => {
        // 📋 YOUR JSON DATA - Replace this array with your actual API response
        const availabilityArray = @json(isset($service) ? $service->availabilities : []);

        // Convert array format to week-based format for the calendar
        availabilityArray.forEach(item => {
            const date = moment(item.date);
            const weekStart = date.clone().startOf('isoWeek'); // Get Monday of that week
            const weekKey = weekStart.format("YYYY-MM-DD");
            const dayName = item.day;

            // Convert time format from "HH:MM:SS" to "HH:MM"
            const startTime = item.start_time.substring(0, 5); // Remove seconds
            const endTime = item.end_time.substring(0, 5); // Remove seconds

            // Initialize week if it doesn't exist
            if (!weekData[weekKey]) {
                weekData[weekKey] = {
                    Monday: { enabled: false, start: "10:00", end: "19:00" },
                    Tuesday: { enabled: false, start: "10:00", end: "19:00" },
                    Wednesday: { enabled: false, start: "10:00", end: "19:00" },
                    Thursday: { enabled: false, start: "10:00", end: "19:00" },
                    Friday: { enabled: false, start: "10:00", end: "19:00" },
                    Saturday: { enabled: false, start: "10:00", end: "19:00" },
                    Sunday: { enabled: false, start: "10:00", end: "19:00" }
                };
            }

            // Set the specific day data (for all dates including past dates)
            weekData[weekKey][dayName] = {
                enabled: true,
                start: startTime,
                end: endTime,
                id: item.id, // Store original ID for reference
                service_id: item.service_id // Store service_id for reference
            };
        });
    };

    // 🎯 EXTRACT SELECTED AVAILABILITY IN YOUR DESIRED FORMAT
    const getSelectedAvailability = () => {
        const selectedAvailability = [];

        // Loop through all weeks in weekData
        Object.keys(weekData).forEach(weekKey => {
            const weekStart = moment(weekKey); // Monday of the week
            const weekDays = weekData[weekKey];

            // Check each day of the week
            Object.keys(weekDays).forEach(dayName => {
                const dayData = weekDays[dayName];

                // Only include enabled days
                if (dayData.enabled) {
                    // Calculate the actual date for this day
                    const dayIndex = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].indexOf(dayName);
                    const actualDate = weekStart.clone().add(dayIndex, 'days');

                    // Add to result array in your desired format
                    selectedAvailability.push({
                        "date": actualDate.format("YYYY-MM-DD"),
                        "day": dayName,
                        "start": dayData.start,
                        "end": dayData.end
                    });
                }
            });
        });

        // Sort by date for better organization
        selectedAvailability.sort((a, b) => moment(a.date).diff(moment(b.date)));
        return selectedAvailability;
    };

    // 📝 UPDATE TEXTAREA WITH JSON OUTPUT
    const updateJsonOutput = () => {
        const selectedData = getSelectedAvailability();
        const jsonString = JSON.stringify(selectedData, null, 2);
        $("#jsonOutput").val(jsonString);
    };

    const updateWeekUI = () => {
        const startOfWeek = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const weekDays = Array.from({ length: 7 }, (_, i) => startOfWeek.clone().add(i, "days"));
        const weekRange = `${weekDays[0].format("DD MMM YYYY")} - ${weekDays[6].format("DD MMM YYYY")}`;
        const weekKey = startOfWeek.format("YYYY-MM-DD");
        const week = weekData[weekKey] || {};

        $("#weekRange").text(weekRange);

        // Update navigation buttons
        $("#prevWeek").prop('disabled', currentWeekIndex <= 0);

        const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
        const today = moment().startOf('day');

        // Update existing day rows with saved data
        dayNames.forEach((day, index) => {
            const date = weekDays[index];
            const val = week[day] || { start: "10:00", end: "19:00", enabled: false };
            const isPastDate = date.isBefore(today, 'day');

            const $dayRow = $(`.day-row[data-day="${day}"]`);
            const $checkbox = $dayRow.find('.day-checkbox');

            // Set data-date attribute
            $dayRow.attr('data-date', date.format("YYYY-MM-DD"));

            // Update checkbox state without triggering change event
            $checkbox.off('change');
            $checkbox.prop('checked', val.enabled);
            // Don't disable past dates - they can still be checked

            // Update content based on state
            if (val.enabled) {
                // Show time inputs
                const $status = $dayRow.find('.day-status');
                if ($status.length > 0) {
                    $status.replaceWith(`
                        <div class="time-inputs">
                            <input type="time" class="form-control form-control-sm start-time" value="${val.start}" data-day="${day}">
                            <span>to</span>
                            <input type="time" class="form-control form-control-sm end-time" value="${val.end}" data-day="${day}">
                        </div>
                    `);
                } else {
                    $dayRow.find('.start-time').val(val.start);
                    $dayRow.find('.end-time').val(val.end);
                }
            } else {
                // Show status text - always show "Closed" regardless of past/future date
                const statusText = "Closed";
                const $timeInputs = $dayRow.find('.time-inputs');
                if ($timeInputs.length > 0) {
                    $timeInputs.replaceWith(`<span class="day-status">${statusText}</span>`);
                } else {
                    $dayRow.find('.day-status').text(statusText);
                }
            }

            // Re-attach change event
            $checkbox.on('change', function() {
                const $dayRow = $(this).closest('.day-row');
                const day = $(this).data('day');
                const isChecked = $(this).is(':checked');

                if (isChecked) {
                    const $status = $dayRow.find('.day-status');
                    if ($status.length > 0) {
                        $status.replaceWith(`
                            <div class="time-inputs">
                                <input type="time" class="form-control form-control-sm start-time" value="10:00" data-day="${day}">
                                <span>to</span>
                                <input type="time" class="form-control form-control-sm end-time" value="19:00" data-day="${day}">
                            </div>
                        `);
                    }
                } else {
                    const $timeInputs = $dayRow.find('.time-inputs');
                    if ($timeInputs.length > 0) {
                        $timeInputs.replaceWith('<span class="day-status">Closed</span>');
                    }
                }

                saveCurrentWeekData();
                updateJsonOutput();
            });
        });
    };

    const saveCurrentWeekData = () => {
        const base = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const key = base.format("YYYY-MM-DD");
        weekData[key] = {};

        $(".day-row").each(function() {
            const day = $(this).data("day");
            const enabled = $(this).find(".day-checkbox").is(":checked");
            const $startTime = $(this).find(".start-time");
            const $endTime = $(this).find(".end-time");
            const start = $startTime.length > 0 ? $startTime.val() : "10:00";
            const end = $endTime.length > 0 ? $endTime.val() : "19:00";
            weekData[key][day] = { enabled, start, end };
        });

        console.log('Saved week data:', weekData[key]);
    };

    // Function to duplicate the weeks (with reset functionality)
    const duplicateWeeks = (weeks) => {
        const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
        const srcKey = current.format("YYYY-MM-DD");

        // 🔄 RESET: Clear all future week duplications first
        Object.keys(weekData).forEach(weekKey => {
            const weekDate = moment(weekKey);
            const currentWeekDate = moment(srcKey);

            // Remove any week that's after the current week and was previously duplicated
            if (weekDate.isAfter(currentWeekDate, 'week')) {
                // Check if this week has the same pattern as current week (indicating it was duplicated)
                const currentWeekData = weekData[srcKey];
                const weekToCheck = weekData[weekKey];

                // Only remove if it looks like a duplication (same enabled pattern)
                if (currentWeekData && weekToCheck) {
                    const currentEnabledDays = Object.keys(currentWeekData).filter(day => currentWeekData[day].enabled);
                    const checkEnabledDays = Object.keys(weekToCheck).filter(day => weekToCheck[day].enabled);

                    // If same number of enabled days, likely a duplication - remove it
                    if (currentEnabledDays.length === checkEnabledDays.length && currentEnabledDays.length > 0) {
                        delete weekData[weekKey];
                    }
                }
            }
        });

        // 📅 CREATE: Now create fresh duplications for the selected weeks
        for (let i = 1; i < weeks; i++) {
            const next = current.clone().add(i * 7, "days");
            const newKey = next.format("YYYY-MM-DD");
            // Create deep copy of current week's data
            weekData[newKey] = JSON.parse(JSON.stringify(weekData[srcKey]));
        }
    };

    // Validate time inputs
    const validateTimeInput = (input) => {
        const $input = $(input);
        const $dayRow = $input.closest('.day-row');
        const startTime = $dayRow.find('.start-time').val();
        const endTime = $dayRow.find('.end-time').val();
        const dayName = $dayRow.data('day');

        // Validate that both times are present
        if (!startTime || !endTime) {
            return true; // Allow empty values for now
        }

        // Validate 24-hour format (HH:MM)
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        if (!timeRegex.test(startTime) || !timeRegex.test(endTime)) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Time Format',
                text: 'Please use 24-hour format (HH:MM)',
                confirmButtonText: 'OK'
            });
            $input.focus();
            return false;
        }

        // Validate that start time is before end time
        if (startTime >= endTime) {
            Swal.fire({
                icon: 'error',
                title: 'Invalid Time Range',
                text: `End time must be after start time for ${dayName}`,
                confirmButtonText: 'OK'
            });
            $input.focus();
            return false;
        }

        return true;
    };

    // Make functions globally available for form submission
    window.getSelectedAvailability = getSelectedAvailability;
    window.saveCurrentWeekData = saveCurrentWeekData;

    // Initialize data from JSON
    initializeDataFromJSON();
    updateWeekUI();
    updateJsonOutput();

    // Initialize modal when it's shown
    $('#availabilityModal').on('shown.bs.modal', function() {
        updateWeekUI();
        updateJsonOutput();
    });



        // Validate time inputs when they change
        $(document).on("change", ".start-time, .end-time", function() {
            if (validateTimeInput(this)) {
                saveCurrentWeekData();
                updateJsonOutput(); // Update JSON when time changes
            }
        });

        $("#prevWeek").click(function() {
            // Prevent going to past weeks
            if (currentWeekIndex > 0) {
                saveCurrentWeekData();
                currentWeekIndex--;
                updateWeekUI();
                updateJsonOutput(); // Update JSON when week changes
            }
        });

        $("#nextWeek").click(function() {
            saveCurrentWeekData();
            currentWeekIndex++;
            updateWeekUI();
            updateJsonOutput(); // Update JSON when week changes
        });

        // Function to reset modal to initial state
        const resetModal = () => {
            // Reset all checkboxes
            $('.day-checkbox').prop('checked', false);

            // Reset all day statuses to "Closed"
            $('.day-row').each(function() {
                const $timeInputs = $(this).find('.time-inputs');
                if ($timeInputs.length > 0) {
                    $timeInputs.replaceWith('<span class="day-status">Closed</span>');
                }
            });

            // Reset recurring options
            $('#recurringCheckbox').prop('checked', false);
            $('#recurringOptions').hide();
            $('#customWeeksInput').hide();
            $('input[name="recurringWeeks"]').prop('checked', false);
            $('#customWeeksNumber').val('');

            // Clear week data
            Object.keys(weekData).forEach(key => {
                delete weekData[key];
            });

            // Reset to current week
            currentWeekIndex = 0;
            updateWeekUI();
            updateJsonOutput();
        };

        // Cancel button handler
        $("#cancelAvailability").click(function() {
            resetModal();
            $('#availabilityModal').modal('hide');
        });

        // Done button handler
        $("#saveAvailability").click(function() {
            // Validate all selected days have proper times
            let hasValidationError = false;

            $('.day-checkbox:checked').each(function() {
                const $dayRow = $(this).closest('.day-row');
                const $startTime = $dayRow.find('.start-time');
                const $endTime = $dayRow.find('.end-time');

                if ($startTime.length > 0 && $endTime.length > 0) {
                    if (!validateTimeInput($startTime[0])) {
                        hasValidationError = true;
                        return false; // Break the loop
                    }
                }
            });

            if (!hasValidationError) {
                saveCurrentWeekData();
                updateJsonOutput(); // Final update of JSON
                const selectedData = getSelectedAvailability();
                console.log("Availability saved:", selectedData);
                $('#availabilityModal').modal('hide');
            }
        });

        // Function to show toast notification
        const showToast = (message, type = 'success') => {
            // Remove any existing toast
            $('.toast-notification').remove();

            let bgClass, iconClass;
            switch(type) {
                case 'success':
                    bgClass = 'bg-success';
                    iconClass = 'check-circle';
                    break;
                case 'danger':
                    bgClass = 'bg-danger';
                    iconClass = 'exclamation-circle';
                    break;
                case 'warning':
                    bgClass = 'bg-warning';
                    iconClass = 'exclamation-triangle';
                    break;
                default:
                    bgClass = 'bg-success';
                    iconClass = 'check-circle';
            }

            const toastHtml = `
                <div class="toast-notification position-fixed top-0 end-0 m-3" style="z-index: 9999;">
                    <div class="toast show ${bgClass} text-white" role="alert">
                        <div class="toast-body d-flex align-items-center">
                            <i class="fas fa-${iconClass} me-2"></i>
                            ${message}
                        </div>
                    </div>
                </div>
            `;

            $('body').append(toastHtml);

            // Auto remove after 3 seconds
            setTimeout(() => {
                $('.toast-notification').fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        };

        // Function to check if any day is selected
        const hasSelectedDays = () => {
            return $('.day-checkbox:checked').length > 0;
        };

        // Recurring Checkbox Change
        $("#recurringCheckbox").change(function() {
            const isChecked = $(this).is(':checked');
            if (isChecked) {
                // Check if any day is selected
                if (!hasSelectedDays()) {
                    // Prevent checking and show warning toast
                    $(this).prop('checked', false);
                    showToast('Please select a day first', 'warning');
                    return;
                }

                // Show recurring options
                $("#recurringOptions").show();
                // Don't auto-select any radio button
                console.log('Recurring enabled - showing options');
            } else {
                // Hide recurring options
                $("#recurringOptions").hide();
                $("#customWeeksInput").hide();
                // Clear all radio buttons
                $('input[name="recurringWeeks"]').prop('checked', false);
                $("#customWeeksNumber").val('');

                // Clear future weeks when recurring is disabled
                const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                const srcKey = current.format("YYYY-MM-DD");

                Object.keys(weekData).forEach(weekKey => {
                    const weekDate = moment(weekKey);
                    const currentWeekDate = moment(srcKey);

                    if (weekDate.isAfter(currentWeekDate, 'week')) {
                        delete weekData[weekKey];
                    }
                });
                updateJsonOutput();

                // Show toast notification for recurring removal
                showToast('Recurring schedule removed', 'danger');
                console.log('Recurring disabled - cleared future weeks');
            }
        });

        // Handle recurring weeks radio button changes
        $('input[name="recurringWeeks"]').change(function() {
            const selectedValue = $(this).val();

            if (selectedValue === 'custom') {
                $("#customWeeksInput").show();
                $("#customWeeksNumber").focus();
            } else {
                $("#customWeeksInput").hide();
                $("#customWeeksNumber").val('');

                // Apply the selected number of weeks
                saveCurrentWeekData();
                const weeks = parseInt(selectedValue);
                duplicateWeeks(weeks);
                updateJsonOutput();

                // Show success toast
                showToast(`Your schedule is copied for ${weeks} weeks`, 'success');
                console.log(`Recurring set to ${weeks} weeks`);
            }
        });

        // Handle Apply button for custom weeks
        $("#applyCustomWeeks").click(function() {
            const customWeeks = parseInt($("#customWeeksNumber").val());
            if (customWeeks && customWeeks > 0 && customWeeks <= 52) {
                saveCurrentWeekData();
                duplicateWeeks(customWeeks);
                updateJsonOutput();

                // Show success toast
                showToast(`Your schedule is copied for ${customWeeks} weeks`, 'success');
                console.log(`Custom recurring set to ${customWeeks} weeks`);
            } else {
                alert('Please enter a valid number of weeks (1-52)');
                $("#customWeeksNumber").focus();
            }
        });

        // Handle Enter key press in custom weeks input
        $("#customWeeksNumber").keypress(function(e) {
            if (e.which === 13) { // Enter key
                $("#applyCustomWeeks").click();
            }
        });
    })(); // Close IIFE
}); // Close outer jQuery ready
</script>
@endpush
