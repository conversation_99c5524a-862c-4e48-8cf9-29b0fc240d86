<?php

namespace App\Http\Controllers;

use App\Models\Subscription;
use App\Models\User;
use App\Models\UserSubscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Stripe\Customer;
use Stripe\Stripe;
use Stripe\StripeClient;
use Stripe\Webhook;

class StripeController extends Controller
{
    protected $stripe;

    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));
        $this->stripe = new StripeClient(config('services.stripe.secret_key'));
    }

    public function purchaseSubscription(Request $req)
    {
        $subscription_id = $req->subscription_id;
        $user = auth()->user();

        $subscription = Subscription::where("id", $subscription_id)->first();

        if (!$subscription) {
            return redirect()->to_route('subscriptions.index')->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Subscription not found!'
            ]);
        }

        if (!$user->customer_id) {
            try {
                $customer = Customer::create([
                    'email' => $user->email,
                ]);
                $user->customer_id = $customer->id;
                $user->save();
            } catch (\Exception $e) {
                return redirect()->back()->with([
                    'title' => 'Error',
                    'type' => 'error',
                    'message' => 'Failed to create Stripe customer: ' . $e->getMessage()
                ]);
            }
        }

        $existingSubscription = UserSubscription::where('user_id', $user->id)->where('subscription_id', $subscription_id)->where('status', 1)->first();
        if ($existingSubscription) {
            return redirect()->back()->with([
                'title' => 'Already Subscribed',
                'type' => 'info',
                'message' => 'You are already subscribed to this plan.'
            ]);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret_key'));

            $checkoutSession = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [
                    [
                        'price' => $subscription->pricing_id,
                        'quantity' => 1,
                    ],
                ],
                'mode' => 'subscription',
                'customer' => $user->customer_id,
                'success_url' => route('subscription.success'),
                'cancel_url' => route('subscription.failed'),
            ]);

            session()->put([
                'checkout_id' => $checkoutSession->id,
            ]);

            return redirect($checkoutSession->url);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Failed to create Stripe Checkout session: ' . $e->getMessage()
            ]);
        }
    }

    public function subscriptionSuccess()
    {
        $checkout_id = session('checkout_id');
        try {
            $session = \Stripe\Checkout\Session::retrieve($checkout_id);
            $user = auth()->user();

            if ($session->payment_status === 'paid') {
                $this->user_notification(
                    $user->id,
                    'Payment Successful',
                    'You have successfully paid $' . $session->amount_total / 100 . ' for subscription'
                );


                return redirect()->route('subscriptions.index')->with([
                    'title' => 'Subscribed Successfully',
                    'type' => 'success',
                    'message' => 'Plan subscribed successfully'
                ]);
            }

            return redirect()->route('subscriptions.index')->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Payment failed or was not completed.'
            ]);
        } catch (\Exception $e) {
            return redirect()->route('subscriptions.index')->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Error processing payment: ' . $e->getMessage()
            ]);
        }
    }

    public function handleStripeWebhook(Request $request)
    {
        Stripe::setApiKey(config('services.stripe.secret_key'));
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        $endpoint_secret = config('services.stripe.webhook_secret');

        try {
            $event = Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
            switch ($event->type) {
                case 'invoice.payment_succeeded':
                    $invoice = $event->data->object;
                    $subscriptionId = $invoice->subscription;
                    $userId = $invoice->customer;
                    $this->subscriptionSucceeded($subscriptionId, $userId, $invoice);
                    break;

                case 'customer.subscription.updated':
                    $subscription = $event->data->object;
                    $this->subscriptionUpdated($subscription);
                    break;

                case 'customer.subscription.deleted':
                    $subscription = $event->data->object;
                    $userId = $subscription->customer;
                    $this->subscriptionCancelled($subscription, $userId);
                    break;

                case 'invoice.payment_failed':
                    break;

                case 'customer.subscription.created':
                    break;

                default:
                    return response()->json(['status' => 'ok']);
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Webhook Error: ' . $e->getMessage(). $e->getLine()], 400);
        }
    }

    protected function subscriptionSucceeded($subscriptionId, $userId, $invoice)
    {
        $billingReason = $invoice->billing_reason;
        $user = User::where('customer_id', $userId)->first();
        if (!$user) {
            return;
        }
        $existingSubscription = UserSubscription::where('user_id', $user->id)->where('status', 1)->first();
        $subscription = Subscription::where('pricing_id', $invoice->lines->data[0]->pricing->price_details->price)->first();
        if ($existingSubscription) {
            $existingSubscription->status = 0;
            $existingSubscription->save();
            if ($billingReason === 'subscription_create') {
                try {
                    $stripeSubscription = \Stripe\Subscription::retrieve($existingSubscription->stripe_subscription_id);
                    $stripeSubscription->cancel();
                } catch (\Stripe\Exception\ApiErrorException $e) {
                    return back()->with(["type" => "error", "message" => $e->getMessage(), "title" => "Error"]);
                }
            }
        }
        $role = Role::where("name", $subscription->type)->first();
        $user->syncRoles($role);
        $newSubscription = new UserSubscription;
        $newSubscription->user_id = $user->id;
        $newSubscription->subscription_id = $subscription->id;
        $newSubscription->subscription_type = $subscription->type;
        $newSubscription->subscription_price = $subscription->price;
        $newSubscription->subscription_booking_fee = $subscription->booking_fee;
        $newSubscription->subscription_total_service = $subscription->total_service;
        $newSubscription->stripe_subscription_id = $subscriptionId;
        $newSubscription->status = 1;
        $newSubscription->start_date = Carbon::now();
        $newSubscription->end_date = Carbon::now()->addMonth();
        $newSubscription->save();
        // Get user's image for subscription notification
        $userImage = $user->profile->pic ?? 'no_avatar.jpg';

        $this->user_notification($user->id, 'Subscribed Successfully',  'You have successfully subscribed!', $userImage);
    }

    protected function subscriptionUpdated($subscription)
    {
        $updatedPriceId = $subscription->plan->id;
        $currentStripeSubscription = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();

        if ($currentStripeSubscription) {
            $planId = $subscription->plan->id;

            $usersToNotify = Subscription::where('plan_id', $planId)
                ->where('status', 1)
                ->get();

            foreach ($usersToNotify as $user) {
                // Get user's image for plan update notification
                $userObj = User::find($user->user_id);
                $userImage = $userObj->profile->pic ?? 'no_avatar.jpg';

                $this->user_notification($user->user_id, 'Plan Updated',  'Your subscribed plan price has changed!', $userImage);
            }
        }
    }

    protected function subscriptionCancelled($subscription, $userId)
    {
        $user = User::where('customer_id', $userId)->first();
        $subscriptionRecord = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();
        if ($subscriptionRecord) {
            $subscriptionRecord->status = 0;
            $subscriptionRecord->save();
            $this->user_notification($user->id, 'Subscription cancelled', 'Your subscription has been cancelled.');
        }
    }

    public function cancel_subscription(Request $req)
    {
        $stripe_subscription_id = $req->stripe_subscription_id;
        try {
            $subscription = \Stripe\Subscription::retrieve($stripe_subscription_id);

            if (!$subscription || $subscription->status !== 'active') {
                return redirect()->route('subscriptions.index')->with(['title' => 'Error', 'type' => 'error', 'message' => 'Subscription not found or not active!']);
            }
            $subscription->cancel();
            return redirect()->route('subscriptions.index')->with(['title' => 'Success', 'type' => 'success', 'message' => 'Subscription canceled successfully!']);
        } catch (\Exception $e) {
            return redirect()->route('subscriptions.index')->with(['title' => 'Error', 'type' => 'error', 'message' => 'Error canceling subscription: ' . $e->getMessage()]);
        }
    }

    public function subscriptionFailed()
    {
        return redirect()->route('subscriptions.index');
    }
}
