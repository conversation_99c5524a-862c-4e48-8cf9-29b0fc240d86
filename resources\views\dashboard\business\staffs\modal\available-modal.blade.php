<style>
    .availability-modal .modal-dialog {
        max-width: 1100px;
    }

    .availability-modal .section-card {
        border-radius: 10px;
        border: 1px solid var(--input-border);
        background: var(--white);
        padding: 20px;
        margin-bottom: 20px;
    }

    .availability-modal .section-title {
        color: var(--black);
        font-family: Inter;
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--neutral-gray);
    }

    .availability-modal .day-item {
        border-radius: 8px;
        border: 1px solid var(--neutral-gray);
        background: var(--white);
        padding: 15px;
        margin-bottom: 12px;
        transition: all 0.3s ease;
    }

    .availability-modal .day-item:hover {
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .styled-checkbox {
        display: flex;
        align-items: center;
        gap: 10px;
        cursor: pointer;
    }

    .availability-modal .styled-checkbox input[type="checkbox"] {
        appearance: none;
        width: 18px;
        height: 18px;
        border: 2px solid var(--input-border);
        border-radius: 4px;
        background: var(--white);
        cursor: pointer;
        position: relative;
    }

    .availability-modal .styled-checkbox input[type="checkbox"]:checked {
        background: var(--deep-blue);
        border-color: var(--deep-blue);
    }

    .availability-modal .styled-checkbox input[type="checkbox"]:checked::after {
        content: "\f00c";
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        color: var(--white);
        font-size: 10px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .availability-modal .time-input-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .availability-modal .time-input {
        border-radius: 8px;
        border: 1px solid var(--input-border);
        background: var(--white);
        padding: 8px 12px;
        width: 90px;
        font-size: 14px;
        text-align: center;
    }

    .availability-modal .time-input:focus {
        outline: none;
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
    }

    .availability-modal .calendar-controls {
        background: var(--whisper-gray);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .availability-modal .btn-nav {
        border: none;
        background: var(--white);
        border-radius: 6px;
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .btn-nav:hover {
        background: var(--deep-blue);
        color: var(--white);
    }

    .availability-modal .recurring-options {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .availability-modal .radio-option {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 15px;
        border: 1px solid var(--input-border);
        border-radius: 20px;
        background: var(--white);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .radio-option:hover {
        border-color: var(--deep-blue);
    }

    .availability-modal .radio-option input[type="radio"] {
        appearance: none;
        width: 16px;
        height: 16px;
        border: 2px solid var(--input-border);
        border-radius: 50%;
        background: var(--white);
        cursor: pointer;
        position: relative;
    }

    .availability-modal .radio-option input[type="radio"]:checked {
        border-color: var(--deep-blue);
    }

    .availability-modal .radio-option input[type="radio"]:checked::after {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--deep-blue);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .availability-modal .radio-option:has(input:checked) {
        border-color: var(--deep-blue);
        background: rgba(2, 12, 135, 0.05);
    }

    .availability-modal .radio-option.disabled {
        opacity: 0.5;
        pointer-events: none;
        background: var(--whisper-gray);
        border-color: var(--neutral-gray);
    }

    .availability-modal .recurring-options.disabled .radio-option {
        opacity: 0.5;
        pointer-events: none;
        background: var(--whisper-gray);
        border-color: var(--neutral-gray);
    }



    .availability-modal .vacation-badge {
        background: var(--green);
        color: var(--white);
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 12px;
        margin: 2px;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .availability-modal .btn-primary-custom {
        background: var(--deep-blue);
        border: 1px solid var(--deep-blue);
        color: var(--white);
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .btn-primary-custom:hover {
        background: var(--ocean-blue);
        border-color: var(--ocean-blue);
    }

    .availability-modal .btn-outline-custom {
        background: transparent;
        border: 1px solid var(--input-border);
        color: var(--black);
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .availability-modal .btn-outline-custom:hover {
        border-color: var(--deep-blue);
        color: var(--deep-blue);
    }

    .availability-modal .max-height-300 {
        max-height: 300px;
    }

    .availability-modal .overflow-auto {
        overflow-y: auto;
    }

    .availability-modal .schedule-item {
        border: 1px solid var(--neutral-gray);
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        background: var(--white);
        transition: all 0.3s ease;
    }

    .availability-modal .schedule-item:hover {
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    }

    .availability-modal .day-item input:disabled {
        background: var(--whisper-gray);
        color: var(--gray);
        cursor: not-allowed;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .time-input-group {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .time-input-group {
        display: flex !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .closed-text {
        display: block !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .closed-text {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .add-time-slot-container {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .add-time-slot-container {
        display: block !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:not(:checked)) .additional-time-slots {
        display: none !important;
    }

    .availability-modal .day-item:has(input[type="checkbox"]:checked) .additional-time-slots {
        display: block !important;
    }

    .availability-modal .add-time-slot-btn {
        background: var(--deep-blue);
        color: var(--white) !important;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
        margin: 8px auto 0;
        transition: all 0.3s ease;
    }

    .availability-modal .add-time-slot-btn i {
        color: var(--white) !important;
    }

    .availability-modal .add-time-slot-btn:hover {
        background: var(--ocean-blue);
        transform: scale(1.1);
    }

    .availability-modal .additional-time-slots {
        margin-top: 10px;
    }

    .availability-modal .additional-time-slot {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 8px;
        padding: 8px;
        background: var(--whisper-gray);
        border-radius: 6px;
    }

    .availability-modal .remove-time-slot-btn {
        background: var(--red);
        color: var(--white);
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 10px;
        transition: all 0.3s ease;
    }

    .availability-modal .remove-time-slot-btn:hover {
        background: #dc2626;
        transform: scale(1.1);
    }

    /* Time picker arrows styling */
    .availability-modal .time-input {
        position: relative;
    }

    .availability-modal .time-input-wrapper {
        position: relative;
        display: inline-block;
    }

    .availability-modal .time-arrows {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 2px;
        z-index: 10;
    }

    .availability-modal .time-arrow {
        width: 16px;
        height: 12px;
        background: var(--deep-blue);
        color: white;
        border: none;
        cursor: pointer;
        font-size: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        transition: all 0.2s ease;
    }

    .availability-modal .time-arrow:hover {
        background: var(--ocean-blue);
        transform: scale(1.1);
    }

    .availability-modal .time-arrow.up {
        margin-bottom: 1px;
    }

    .availability-modal .time-arrow.down {
        margin-top: 1px;
    }

    /* Time input validation styling */
    .availability-modal .time-input.is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .availability-modal .time-error {
        font-size: 11px;
        color: #dc3545;
        margin-top: 4px;
    }

    .availability-modal .time-input {
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .availability-modal .time-input:focus {
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        outline: 0;
    }

    .availability-modal .time-input {
        cursor: pointer;
    }

    .availability-modal .time-input:focus {
        border-color: var(--deep-blue);
        box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
    }

    .availability-modal .flatpickr-input {
        cursor: pointer !important;
    }

    /* Vacation Calendar Modal Specific Styles */
    .vacation-calendar-modal .modal-dialog {
        max-width: 1000px;
    }

    .vacation-calendar-modal .vacation-calendar-container {
        /* min-height: 400px; */
        display: flex;
        justify-content: center;
        align-items: flex-start;
    }

    .vacation-calendar-modal .vacation-preview-container {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid var(--neutral-gray);
        border-radius: 8px;
        background: var(--whisper-gray);
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge {
        display: block;
        margin-bottom: 8px;
        padding: 8px 12px;
        background: var(--green);
        color: var(--white);
        border-radius: 6px;
        font-size: 13px;
        position: relative;
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge .btn-close {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 10px;
        opacity: 0.8;
    }

    .vacation-calendar-modal .vacation-preview-container .vacation-badge:hover .btn-close {
        opacity: 1;
    }

    /* Flatpickr Calendar Customization */
    .vacation-calendar-modal .flatpickr-calendar {
        width: 100% !important;
        max-width: 100% !important;
        font-size: 14px;
    }

    .vacation-calendar-modal .flatpickr-months {
        padding: 15px;
    }

    .vacation-calendar-modal .flatpickr-month {
        height: auto;
    }

    .vacation-calendar-modal .flatpickr-current-month {
        font-size: 18px;
        font-weight: 600;
        color: var(--deep-blue);
    }

    .vacation-calendar-modal .flatpickr-weekdays {
        background: var(--whisper-gray);
        padding: 10px 0;
    }

    .vacation-calendar-modal .flatpickr-weekday {
        font-weight: 600;
        color: var(--black);
        font-size: 13px;
    }

    .vacation-calendar-modal .flatpickr-days {
        padding: 10px;
    }

    .vacation-calendar-modal .flatpickr-day {
        width: 40px;
        height: 40px;
        line-height: 40px;
        margin: 2px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
    }

    .vacation-calendar-modal .flatpickr-day:hover {
        background: var(--ice-blue);
        border-color: var(--deep-blue);
    }

    .vacation-calendar-modal .flatpickr-day.selected {
        background: var(--deep-blue) !important;
        border-color: var(--deep-blue) !important;
        color: var(--white) !important;
    }

    .vacation-calendar-modal .flatpickr-day.selected:hover {
        background: var(--ocean-blue) !important;
    }



    /* Toastr positioning for modal */
    .toast-container {
        z-index: 999999 !important;
    }



    /* Responsive adjustments */
    @media (max-width: 991px) {
        .availability-modal .modal-dialog {
            max-width: 95%;
            margin: 1rem auto;
        }

        .availability-modal .section-card {
            padding: 15px;
            margin-bottom: 15px;
        }

        .availability-modal .day-item {
            padding: 12px;
        }

        .availability-modal .time-input-group {
            flex-direction: column;
            gap: 8px;
            align-items: stretch;
        }

        .availability-modal .time-input {
            width: 100%;
        }

        .availability-modal .recurring-options {
            flex-direction: column;
            gap: 10px;
        }

        .availability-modal .radio-option {
            justify-content: center;
        }
    }

    @media (max-width: 767px) {
        .availability-modal .modal-dialog {
            margin: 0.5rem;
        }

        .availability-modal .day-item .d-flex {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .availability-modal .calendar-controls {
            padding: 10px;
        }

        .availability-modal .calendar-controls h6 {
            font-size: 12px;
        }
    }
</style>

<div class="modal fade availability-modal" id="availabilityModal" tabindex="-1"
    aria-labelledby="availabilityModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-4">
                <h5 class="modal-title fs-18 semi-bold black" id="availabilityModalLabel">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                    Staff Availability Schedule
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <form>
                    <div class="row">
                        <!-- Left Column: Weekly Schedule -->
                        <div class="col-lg-7">
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-clock me-2"></i>
                                    Weekly Schedule
                                </h6>

                                <!-- Calendar Navigation -->
                                <div class="calendar-controls d-flex justify-content-between align-items-center">
                                    <button type="button" id="prev-week" class="btn-nav">
                                        <i class="fa-solid fa-chevron-left"></i>
                                    </button>
                                    <h6 id="date-range" class="m-0 fs-14 semi-bold black">Loading...</h6>
                                    <button type="button" id="next-week" class="btn-nav">
                                        <i class="fa-solid fa-chevron-right"></i>
                                    </button>
                                </div>

                                <!-- Days Schedule -->
                                <div class="days-schedule mt-3">
                                    <!-- Monday -->
                                    <div class="day-item" data-day="Monday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="monday-check">
                                                <input type="checkbox" class="day-toggle" id="monday-check">
                                                <span class="fs-14 semi-bold black">Monday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time timePicker" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time timePicker" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Monday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Tuesday -->
                                    <div class="day-item" data-day="Tuesday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="tuesday-check">
                                                <input type="checkbox" class="day-toggle" id="tuesday-check">
                                                <span class="fs-14 semi-bold black">Tuesday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time timePicker" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time timePicker" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Tuesday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Wednesday -->
                                    <div class="day-item" data-day="Wednesday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="wednesday-check">
                                                <input type="checkbox" class="day-toggle" id="wednesday-check">
                                                <span class="fs-14 semi-bold black">Wednesday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time timePicker" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time timePicker" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Wednesday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Thursday -->
                                    <div class="day-item" data-day="Thursday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="thursday-check">
                                                <input type="checkbox" class="day-toggle" id="thursday-check">
                                                <span class="fs-14 semi-bold black">Thursday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time timePicker" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time timePicker" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Thursday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Friday -->
                                    <div class="day-item" data-day="Friday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="friday-check">
                                                <input type="checkbox" class="day-toggle" id="friday-check">
                                                <span class="fs-14 semi-bold black">Friday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time timePicker" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time timePicker" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Friday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Saturday -->
                                    <div class="day-item" data-day="Saturday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="saturday-check">
                                                <input type="checkbox" class="day-toggle" id="saturday-check">
                                                <span class="fs-14 semi-bold black">Saturday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time timePicker" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time timePicker" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Saturday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Sunday -->
                                    <div class="day-item" data-day="Sunday">
                                        <div class="d-flex align-items-center justify-content-between">
                                            <label class="styled-checkbox" for="sunday-check">
                                                <input type="checkbox" class="day-toggle" id="sunday-check">
                                                <span class="fs-14 semi-bold black">Sunday</span>
                                            </label>
                                            <div class="time-input-group" style="display: none;">
                                                <input class="time-input start-time timePicker" value="10:00 AM" type="text"
                                                    placeholder="e.g. 10:00 AM" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input end-time timePicker" value="7:00 PM" type="text"
                                                    placeholder="e.g. 7:00 PM" />
                                            </div>
                                            <span class="closed-text text-muted fs-13">Closed</span>
                                        </div>
                                        <div class="additional-time-slots" style="display: none;">
                                            <!-- Additional time slots will be added here -->
                                        </div>
                                        <div class="add-time-slot-container" style="display: none;">
                                            <button type="button" class="add-time-slot-btn" data-day="Sunday">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column: Settings & Options -->
                        <div class="col-lg-5">

                            <!-- Recurring Schedule Settings -->
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-repeat me-2"></i>
                                    Recurring Schedule
                                    <input type="checkbox" name="recurring" value="1">
                                </h6>
                                <div class="recurring-options">
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="4">
                                        <span class="fs-13">4 Weeks</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="8">
                                        <span class="fs-13">8 Weeks</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="duration" value="custom">
                                        <span class="fs-13">Custom</span>
                                    </label>
                                </div>

                                <div class="custom-weeks mt-3" id="custom-weeks" style="display: none;">
                                    <label class="form-label form-input-labels">Number of Weeks</label>
                                    <div class="d-flex gap-2">
                                        <input type="number" class="form-control form-inputs-field"
                                            id="custom-weeks-input" placeholder="Enter number of weeks"
                                            min="1" max="52">
                                        <button type="button" class="btn-primary-custom"
                                            id="apply-custom-weeks-btn">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Vacation Days Section -->
                            <div class="section-card">
                                <h6 class="section-title">
                                    <i class="fas fa-umbrella-beach me-2"></i>
                                    Vacation Days
                                </h6>

                                <p class="fs-12 text-muted mb-3">Select dates when you will be unavailable</p>

                                <div class="mb-3">
                                    <button type="button" class="btn-outline-custom" id="vacation-calendar-btn">
                                        <i class="fas fa-calendar-alt me-1"></i> Select Vacation Dates
                                    </button>
                                </div>

                                <div id="selected-vacations" class="mb-3">
                                    <h6 class="fs-12 mb-2">Selected Vacation Dates:</h6>
                                    <div id="vacation-dates-list" class="d-flex flex-wrap gap-1">
                                        <span class="text-muted fs-12">No vacation dates selected</span>
                                    </div>
                                </div>

                                <input type="hidden" id="vacation-dates" name="vacation_dates">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 p-4">
                <div class="d-flex justify-content-end gap-3">
                    <button type="button" class="cancel-btn" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="button" class="save-btn" id="save-availability-btn">
                        <i class="fas fa-check me-1"></i> Save Availability
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Vacation Calendar Modal -->
<div class="modal fade availability-modal vacation-calendar-modal" id="vacationCalendarModal" tabindex="-1"
    aria-labelledby="vacationCalendarModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-4">
                <h5 class="modal-title fs-16 semi-bold black" id="vacationCalendarModalLabel">
                    <i class="fas fa-calendar-alt me-2 text-primary"></i>
                    Select Vacation Dates
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="row">
                    <div class="col-lg-8">
                        <div class="section-card h-100">
                            <h6 class="section-title">
                                <i class="fas fa-calendar me-2"></i>
                                Calendar
                            </h6>
                            <div id="vacation-calendar" class="vacation-calendar-container"></div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="section-card h-100">
                            <h6 class="section-title">
                                <i class="fas fa-list me-2"></i>
                                Selected Dates
                            </h6>
                            <div id="vacation-preview" class="vacation-preview-container">
                                <span class="text-muted fs-12">No dates selected</span>
                            </div>
                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small>Click on dates in the calendar to select vacation days. Selected dates will
                                        be excluded from the staff schedule.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0 p-4">
                <div class="d-flex justify-content-end gap-3">
                    <button type="button" class="btn-outline-custom" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="button" class="btn-primary-custom" id="confirm-vacation-dates">
                        <i class="fas fa-check me-1"></i> Confirm Selection
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script>
        // Toast function for notifications
        function showToast(message, type = 'success') {
            const toastClass = type === 'success' ? 'bg-success' : type === 'warning' ? 'bg-warning' : 'bg-danger';
            const toast = `
                <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                    <div class="d-flex">
                        <div class="toast-body">${message}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;

            $('body').append(toast);
            const toastElement = $('.toast').last();
            const bsToast = new bootstrap.Toast(toastElement[0], {
                delay: 3000
            });
            bsToast.show();

            // Remove toast element after it's hidden
            toastElement.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }

        // ===== AVAILABILITY MODAL MANAGER =====
        const AvailabilityModal = {
            // Global variables
            selectedVacationDates: [],
            weeklySelections: {},

            // Utility functions
            utils: {
                parseTime(timeStr) {
                    if (!timeStr) return null;
                    const match = timeStr.match(/(\d{1,2}):(\d{2})\s*(AM|PM)/i);
                    if (!match) return null;

                    let hours = parseInt(match[1]);
                    const minutes = parseInt(match[2]);
                    const period = match[3].toUpperCase();

                    if (period === 'PM' && hours !== 12) hours += 12;
                    if (period === 'AM' && hours === 12) hours = 0;

                    return hours * 60 + minutes;
                },

                formatTime(minutes) {
                    const hours = Math.floor(minutes / 60);
                    const mins = minutes % 60;
                    const period = hours >= 12 ? 'PM' : 'AM';
                    const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
                    return `${displayHours}:${mins.toString().padStart(2, '0')} ${period}`;
                },

                getNextTimeSlot(timeStr, addMinutes = 60) {
                    const minutes = this.parseTime(timeStr);
                    if (!minutes && minutes !== 0) return '8:00 PM';
                    return this.formatTime(minutes + addMinutes);
                },

                getWeekStart(date) {
                    const d = new Date(date);
                    const day = d.getDay();
                    const diff = d.getDate() - day + (day === 0 ? -6 : 1);
                    return new Date(d.setDate(diff));
                },

                showMessage(input, message) {
                    $(input).parent().find('.validation-message').remove();
                    const $msg = $('<div>', {
                        class: 'validation-message text-danger fs-12 mt-1',
                        text: message
                    });
                    $(input).parent().append($msg);
                    setTimeout(() => $msg.remove(), 3000);
                }
            },

            // Time validation module
            timeValidation: {
                init() {
                    $(document).on('blur', '.time-input', this.validateOnBlur.bind(this));
                },

                validateOnBlur(e) {
                    const $input = $(e.target);
                    const isStart = $input.hasClass('start-time') || $input.hasClass('additional-start-time');
                    const isEnd = $input.hasClass('end-time') || $input.hasClass('additional-end-time');

                    const pairedSelector = isStart ? '.end-time, .additional-end-time' :
                        '.start-time, .additional-start-time';
                    const $paired = $input.parent().find(pairedSelector).first();

                    if ($paired.length) {
                        this.validatePair(isStart ? $input[0] : $paired[0], isEnd ? $input[0] : $paired[0]);
                    }
                },

                validatePair(startInput, endInput) {
                    if (!startInput?.value || !endInput?.value) return;

                    const startMinutes = AvailabilityModal.utils.parseTime(startInput.value);
                    const endMinutes = AvailabilityModal.utils.parseTime(endInput.value);

                    if (startMinutes >= endMinutes) {
                        endInput.value = AvailabilityModal.utils.getNextTimeSlot(startInput.value, 60);
                        AvailabilityModal.utils.showMessage(endInput, 'End time must be after start time');
                    }
                }
            },

            // Vacation management module
            vacation: {
                update() {
                    const $list = $("#vacation-dates-list");
                    const $input = $("#vacation-dates");

                    if (!$list.length || !$input.length) return;

                    if (AvailabilityModal.selectedVacationDates.length === 0) {
                        $list.html('<span class="text-muted fs-12">No vacation dates selected</span>');
                        $input.val('');
                    } else {
                        const badges = AvailabilityModal.selectedVacationDates.map(dateStr => {
                            // Ensure dateStr is a string and handle different formats
                            const dateString = String(dateStr);
                            let formatted = 'Invalid Date';

                            try {
                                // Handle both YYYY-MM-DD and ISO timestamp formats
                                let date;

                                if (dateString.includes('T')) {
                                    // ISO timestamp format: "2025-08-11T00:00:00.000000Z"
                                    date = new Date(dateString);
                                } else if (dateString.includes('-') && dateString.length >= 10) {
                                    // Simple date format: "2025-08-11"
                                    const [year, month, day] = dateString.split('-').map(Number);
                                    if (year && month && day) {
                                        date = new Date(year, month - 1, day); // month is 0-indexed
                                    }
                                }

                                if (date && !isNaN(date.getTime())) {
                                    formatted = date.toLocaleDateString('en-US', {
                                        weekday: 'short',
                                        month: 'short',
                                        day: 'numeric'
                                    });
                                }
                            } catch (error) {
                                console.warn('Error parsing vacation date:', dateString, error);
                            }
                            return `<span class="vacation-badge me-1 mb-1">
                                ${formatted}
                                <button type="button" class="btn-close btn-close-white ms-1"
                                        style="font-size: 0.7em;" onclick="AvailabilityModal.vacation.remove('${dateStr}')">
                                </button>
                            </span>`;
                        });
                        $list.html(badges.join(''));
                        $input.val(AvailabilityModal.selectedVacationDates.join(','));
                    }
                },

                remove(dateStr) {
                    AvailabilityModal.selectedVacationDates = AvailabilityModal.selectedVacationDates.filter(d => d !==
                        dateStr);
                    selectedVacationDates = [...AvailabilityModal.selectedVacationDates]; // Keep global array in sync
                    this.update();
                }
            }
        };

        // Make vacation remove function globally accessible
        window.removeVacationDate = (dateStr) => AvailabilityModal.vacation.remove(dateStr);

        // Expose global variables for backward compatibility
        let selectedVacationDates = [...AvailabilityModal.selectedVacationDates]; // Create a copy
        let weeklySelections = AvailabilityModal.weeklySelections;

        // Expose utility functions for backward compatibility
        function parseTime(timeStr) {
            return AvailabilityModal.utils.parseTime(timeStr);
        }

        function formatTime(minutes) {
            return AvailabilityModal.utils.formatTime(minutes);
        }

        function getNextTimeSlot(timeStr, addMinutes) {
            return AvailabilityModal.utils.getNextTimeSlot(timeStr, addMinutes);
        }

        function showValidationMessage(input, message) {
            AvailabilityModal.utils.showMessage(input, message);
        }

        function updateVacationDisplay() {
            AvailabilityModal.vacation.update();
        }

        function addTimeValidation() {
            /* Legacy function - now handled by timeValidation.init() */
        }

        // Global function for copying schedule to multiple weeks
        window.copyScheduleToMultipleWeeks = function(baseSelections, weeksCount) {
            // Use the current displayed week as the base, not the real current week
            const baseWeekMonday = new Date(AvailabilityModal.weekManager.currentStartDate);

            // First, save current week selections to capture any unsaved additional slots
            AvailabilityModal.weekManager.saveCurrentWeekSelections();

            // Get the updated base selections with current form state
            const currentWeekKey = AvailabilityModal.weekManager.getWeekKey(baseWeekMonday);
            const updatedBaseSelections = AvailabilityModal.weeklySelections[currentWeekKey] || baseSelections;

            // If weeksCount is 1, only apply to current week (don't copy to future weeks)
            if (weeksCount === 1) {
                // Just update the current week with the duration settings
                AvailabilityModal.weeklySelections[currentWeekKey] = {
                    days: updatedBaseSelections.days,
                    duration: updatedBaseSelections.duration,
                    customWeeks: updatedBaseSelections.customWeeks
                };
                return;
            }

            // Copy to each week (starting from current week)
            for (let weekOffset = 0; weekOffset < weeksCount; weekOffset++) {
                const targetWeekMonday = new Date(baseWeekMonday);
                targetWeekMonday.setDate(baseWeekMonday.getDate() + (weekOffset * 7));

                const weekKey = AvailabilityModal.weekManager.getWeekKey(targetWeekMonday);

                // Deep copy the selections to this week (including additionalSlots arrays)
                const copiedDays = {};
                Object.keys(updatedBaseSelections.days).forEach(dayName => {
                    const dayData = updatedBaseSelections.days[dayName];
                    copiedDays[dayName] = {
                        checked: dayData.checked || dayData.enabled,
                        enabled: dayData.enabled || dayData.checked,
                        startTime: dayData.startTime,
                        endTime: dayData.endTime,
                        additionalSlots: dayData.additionalSlots ? [...dayData.additionalSlots.map(
                            slot => ({
                                id: Date.now() + Math
                                    .random(), // Generate new unique ID for each copy
                                startTime: slot.startTime,
                                endTime: slot.endTime
                            }))] : []
                    };
                });

                AvailabilityModal.weeklySelections[weekKey] = {
                    days: copiedDays,
                    duration: updatedBaseSelections.duration,
                    customWeeks: updatedBaseSelections.customWeeks
                };
            }
            // Don't reload selections - keep current form state visible
        };

        // Extend AvailabilityModal with week management
        AvailabilityModal.weekManager = {
            currentStartDate: null,
            $dateRange: $("#date-range"),
            $prevBtn: $("#prev-week"),
            $nextBtn: $("#next-week"),

            init() {
                this.currentStartDate = this.getCurrentWeekMonday();
                this.bindEvents();
                this.updateDateRange();
            },

            getCurrentWeekMonday() {
                const today = new Date();
                const dayOfWeek = today.getDay();
                const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                const monday = new Date(today);
                monday.setDate(today.getDate() + daysToMonday);
                monday.setHours(0, 0, 0, 0);
                return monday;
            },

            normalizeDate(date) {
                const normalized = new Date(date);
                normalized.setHours(0, 0, 0, 0);
                return normalized;
            },

            formatDate(date) {
                return date.toLocaleDateString('en-GB', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            },

            disablePastDays() {
                const today = new Date();
                today.setHours(0, 0, 0, 0); // Normalize to start of day
                const currentWeekMonday = this.getCurrentWeekMonday();
                const dayNames = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

                $(".day-item").each(function() {
                    const $day = $(this);
                    const dayName = $day.attr("data-day");
                    const dayIndex = dayNames.indexOf(dayName);

                    // Calculate the actual date for this day in the current week
                    // currentStartDate should be Monday, so we add dayIndex days to it
                    const dayDate = new Date(AvailabilityModal.weekManager.currentStartDate);
                    dayDate.setDate(dayDate.getDate() + dayIndex);
                    dayDate.setHours(0, 0, 0, 0);

                    // Only disable if the day is from a previous week (not current week)
                    const isCurrentWeek = AvailabilityModal.weekManager.currentStartDate.getTime() ===
                        currentWeekMonday.getTime();
                    const shouldDisable = !isCurrentWeek && dayDate.getTime() < today.getTime();

                    if (shouldDisable) {
                        $day.find(".day-toggle").prop({
                            disabled: true,
                            checked: false
                        });
                        $day.find(".time-input-group").hide();
                        $day.find(".closed-text").show().text("Past Date").css('color', '#999');
                        $day.find(".timePicker").prop('disabled', true).attr('readonly', 'readonly');
                        $day.css({
                            opacity: '0.5',
                            'pointer-events': 'none'
                        });
                    }
                });
            },

            enableAllDays() {
                $(".day-item").each(function() {
                    const $day = $(this);
                    $day.find(".day-toggle").prop('disabled', false);
                    $day.find(".closed-text").text("Closed").css('color', '');
                    $day.css({
                        opacity: '1',
                        'pointer-events': 'auto'
                    });
                    $day.find(".timePicker").prop('disabled', false).removeAttr('readonly');
                });
            },

            updateDateRange() {
                const startDate = new Date(this.currentStartDate);
                const endDate = new Date(this.currentStartDate);
                endDate.setDate(startDate.getDate() + 6);

                this.$dateRange.text(`${this.formatDate(startDate)} - ${this.formatDate(endDate)}`);
                window.currentWeekStart = new Date(startDate);
                window.currentStartDate = new Date(startDate); // Keep global variable in sync

                const currentWeekMonday = this.getCurrentWeekMonday();
                const isCurrentWeekOrEarlier = this.normalizeDate(this.currentStartDate).getTime() <= currentWeekMonday
                    .getTime();

                this.$prevBtn.prop('disabled', isCurrentWeekOrEarlier)
                    .css('opacity', isCurrentWeekOrEarlier ? '0.5' : '1');

                isCurrentWeekOrEarlier ? this.disablePastDays() : this.enableAllDays();
                this.loadWeekSelections();
            },

            getWeekKey(date) {
                const monday = AvailabilityModal.utils.getWeekStart(date);
                return monday.toISOString().split('T')[0];
            },

            saveCurrentWeekSelections() {
                const weekKey = this.getWeekKey(this.currentStartDate);
                const selections = {
                    days: {},
                    duration: null,
                    customWeeks: null,
                    recurring: false
                };

                $(".day-toggle").each(function() {
                    const $checkbox = $(this);
                    const dayName = $checkbox.closest('.day-item').attr('data-day');
                    const isChecked = $checkbox.is(':checked');

                    if (isChecked) {
                        const $dayItem = $checkbox.closest('.day-item');
                        const startTime = $dayItem.find('.start-time').val();
                        const endTime = $dayItem.find('.end-time').val();

                        selections.days[dayName] = {
                            enabled: true,
                            checked: true, // For compatibility
                            startTime: startTime,
                            endTime: endTime,
                            additionalSlots: []
                        };

                        // Collect additional time slots
                        $dayItem.find('.additional-time-slot').each(function() {
                            const $slot = $(this);
                            const additionalStart = $slot.find('.additional-start-time').val();
                            const additionalEnd = $slot.find('.additional-end-time').val();
                            if (additionalStart && additionalEnd) {
                                selections.days[dayName].additionalSlots.push({
                                    startTime: additionalStart,
                                    endTime: additionalEnd
                                });
                            }
                        });
                    } else {
                        selections.days[dayName] = {
                            enabled: false,
                            checked: false // For compatibility
                        };
                    }
                });

                // Save recurring and duration settings
                const isRecurring = $("input[name='recurring']").is(':checked');
                selections.recurring = isRecurring;

                const checkedDuration = $("input[name='duration']:checked");
                if (checkedDuration.length) {
                    selections.duration = checkedDuration.val();
                    if (selections.duration === 'custom') {
                        selections.customWeeks = $("#custom-weeks-input").val();
                    }
                }

                AvailabilityModal.weeklySelections[weekKey] = selections;
            },

            loadWeekSelections() {
                const weekKey = this.getWeekKey(this.currentStartDate);
                let selections = AvailabilityModal.weeklySelections[weekKey];

                // Store current recurring settings before reset
                const currentRecurring = $('input[name="recurring"]').is(':checked');
                const currentDuration = $('input[name="duration"]:checked').val();
                const currentCustomWeeks = $("#custom-weeks-input").val();

                this.resetFormToCleanState();

                // Check if we need to load from edit data
                if (typeof isEditMode !== 'undefined' && isEditMode && (!selections?.days) &&
                    typeof editAvailabilityData !== 'undefined' && editAvailabilityData.length > 0) {
                    selections = this.loadSelectionsFromEditData(this.currentStartDate);
                }

                if (selections && selections.days) {
                    Object.keys(selections.days).forEach(dayName => {
                        const dayData = selections.days[dayName];
                        const $dayItem = $(`[data-day="${dayName}"]`);
                        const $checkbox = $dayItem.find('.day-toggle');

                        if (dayData.enabled) {
                            $checkbox.prop('checked', true);
                            $dayItem.find('.time-input-group').show();
                            $dayItem.find('.closed-text').hide();

                            if (dayData.startTime) $dayItem.find('.start-time').val(dayData.startTime);
                            if (dayData.endTime) $dayItem.find('.end-time').val(dayData.endTime);

                            // Load additional time slots
                            if (dayData.additionalSlots && dayData.additionalSlots.length > 0) {
                                dayData.additionalSlots.forEach(slot => {
                                    AvailabilityModal.timeSlots.add(dayName);
                                    const $lastSlot = $dayItem.find('.additional-time-slot').last();
                                    $lastSlot.find('.additional-start-time').val(slot.startTime);
                                    $lastSlot.find('.additional-end-time').val(slot.endTime);
                                });
                            }
                        }
                    });

                    // Restore recurring and duration settings - prioritize stored selections, then current settings
                    const recurringToRestore = selections.recurring !== undefined ? selections.recurring : currentRecurring;
                    const durationToRestore = selections.duration || currentDuration;
                    const customWeeksToRestore = selections.customWeeks || currentCustomWeeks;

                    // Restore recurring checkbox
                    $('input[name="recurring"]').prop('checked', recurringToRestore);

                    if (durationToRestore && recurringToRestore) {
                        $(`input[name='duration'][value='${durationToRestore}']`).prop('checked', true);

                        if (durationToRestore === 'custom' && customWeeksToRestore) {
                            $("#custom-weeks").show();
                            $("#custom-weeks-input").val(customWeeksToRestore);
                        }
                    } else if (currentRecurring && currentDuration) {
                        // Maintain current recurring state even if no duration is stored for this week
                        $(`input[name='duration'][value='${currentDuration}']`).prop('checked', true);
                        if (currentDuration === 'custom' && currentCustomWeeks) {
                            $("#custom-weeks").show();
                            $("#custom-weeks-input").val(currentCustomWeeks);
                        }
                    }
                }
            },

            resetFormToCleanState() {
                $(".day-toggle").each(function() {
                    const $checkbox = $(this);
                    if ($checkbox.prop('disabled')) return;

                    $checkbox.prop('checked', false);
                    const $parent = $checkbox.closest(".day-item");

                    $parent.find(".time-input-group").hide();
                    $parent.find(".closed-text").show();
                    $parent.find(".timePicker").attr('readonly', 'readonly').prop('disabled', true);
                    $parent.find('.additional-time-slots').empty();
                });

                $("input[name='duration']").prop('checked', false);
                $("#custom-weeks").hide();
            },

            loadSelectionsFromEditData(weekStartDate) {
                const selections = {
                    days: {},
                    duration: null,
                    customWeeks: null
                };
                const weekStart = new Date(weekStartDate);
                const weekEnd = new Date(weekStartDate);
                weekEnd.setDate(weekStart.getDate() + 6);

                // Check if editAvailabilityData exists
                if (typeof editAvailabilityData === 'undefined' || !editAvailabilityData.length) {
                    return selections;
                }

                const weekData = editAvailabilityData.filter(item => {
                    const itemDate = new Date(item.date);
                    return itemDate >= weekStart && itemDate <= weekEnd;
                });

                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

                dayNames.forEach(dayName => {
                    const dayData = weekData.find(item => item.day === dayName);

                    selections.days[dayName] = dayData ? {
                        enabled: true,
                        startTime: dayData.main_slot?.start_time || "10:00 AM",
                        endTime: dayData.main_slot?.end_time || "7:00 PM",
                        additionalSlots: dayData.additional_slots?.map((slot, index) => ({
                            startTime: slot.start_time,
                            endTime: slot.end_time
                        })) || []
                    } : {
                        enabled: false,
                        startTime: "10:00 AM",
                        endTime: "7:00 PM",
                        additionalSlots: []
                    };
                });

                // Check if editRecurringData exists
                if (typeof editRecurringData !== 'undefined' && editRecurringData?.recurring) {
                    selections.duration = editRecurringData.duration;
                    selections.customWeeks = editRecurringData.customWeeks;
                }

                return selections;
            },

            bindEvents() {
                this.$prevBtn.on('click', (e) => {
                    e.preventDefault();
                    const newDate = new Date(this.currentStartDate);
                    newDate.setDate(newDate.getDate() - 7);

                    if (this.normalizeDate(newDate).getTime() < this.getCurrentWeekMonday().getTime()) {
                        this.currentStartDate = this.getCurrentWeekMonday();
                    } else {
                        this.saveCurrentWeekSelections();
                        this.currentStartDate.setDate(this.currentStartDate.getDate() - 7);
                    }
                    window.currentStartDate = new Date(this.currentStartDate); // Keep global in sync
                    this.updateDateRange();
                });

                this.$nextBtn.on('click', (e) => {
                    e.preventDefault();
                    this.saveCurrentWeekSelections();
                    this.currentStartDate.setDate(this.currentStartDate.getDate() + 7);
                    window.currentStartDate = new Date(this.currentStartDate); // Keep global in sync
                    this.updateDateRange();
                });
            }
        };

        // Expose for backward compatibility
        function getWeekStart(date) {
            return AvailabilityModal.utils.getWeekStart(date);
        }

        $(document).ready(function() {
            // Initialize modules
            AvailabilityModal.timeValidation.init();
            AvailabilityModal.weekManager.init();





            // Add time slot management to AvailabilityModal
            AvailabilityModal.timeSlots = {
                add(dayName) {
                    const $dayItem = $(`[data-day="${dayName}"]`);
                    const $container = $dayItem.find('.additional-time-slots');
                    if (!$dayItem.length || !$container.length) return;

                    const lastEndTime = this.getLastEndTime($dayItem[0]);
                    const suggestedStart = AvailabilityModal.utils.getNextTimeSlot(lastEndTime);
                    const suggestedEnd = AvailabilityModal.utils.getNextTimeSlot(suggestedStart, 120);
                    const slotId = Date.now() + Math.random();

                    const slotHtml = `
                        <div class="additional-time-slot" data-slot-id="${slotId}">
                            <input class="time-input additional-start-time timePicker" type="text"
                                   placeholder="e.g. ${suggestedStart}" value="${suggestedStart}" />
                            <span class="text-muted">to</span>
                            <input class="time-input additional-end-time timePicker" type="text"
                                   placeholder="e.g. ${suggestedEnd}" value="${suggestedEnd}" />
                            <button type="button" class="remove-time-slot-btn"
                                    onclick="AvailabilityModal.timeSlots.remove('${dayName}', '${slotId}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;

                    $container.append(slotHtml);

                    // Initialize flatpickr for the newly added time inputs
                    const $newSlot = $container.find(`[data-slot-id="${slotId}"]`);
                    $newSlot.find('.timePicker').each(function() {
                        if (!this._flatpickr) {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "h:i K",
                                time_24hr: false,
                                clickOpens: true,
                                allowInput: false,
                                minuteIncrement: 15,
                                defaultHour: $(this).hasClass('additional-start-time') ? 20 : 22,
                                defaultMinute: 0
                            });
                        }
                    });

                    setTimeout(() => AvailabilityModal.weekManager.saveCurrentWeekSelections(), 100);
                },

                remove(dayName, slotId) {
                    const $dayItem = $(`[data-day="${dayName}"]`);
                    const $slot = $dayItem.find(`[data-slot-id="${slotId}"]`);
                    if ($slot.length) {
                        $slot.remove();
                        setTimeout(() => AvailabilityModal.weekManager.saveCurrentWeekSelections(), 100);
                    }
                },

                getLastEndTime(dayItem) {
                    const $dayItem = $(dayItem);
                    const mainEndTime = $dayItem.find('.end-time').val() || '7:00 PM';
                    const additionalTimes = [];

                    $dayItem.find('.additional-end-time').each(function() {
                        const value = $(this).val();
                        if (value) additionalTimes.push(value);
                    });

                    let latestTime = mainEndTime;
                    let latestMinutes = AvailabilityModal.utils.parseTime(mainEndTime);

                    additionalTimes.forEach(time => {
                        const minutes = AvailabilityModal.utils.parseTime(time);
                        if (minutes && minutes > latestMinutes) {
                            latestTime = time;
                            latestMinutes = minutes;
                        }
                    });

                    return latestTime;
                }
            };

            // Event listeners
            $(document).on('click', '.add-time-slot-btn', function() {
                AvailabilityModal.timeSlots.add($(this).attr('data-day'));
            });

            // Backward compatibility
            window.removeTimeSlot = (dayName, slotId) => AvailabilityModal.timeSlots.remove(dayName, slotId);

            function addTimeSlot(dayName) {
                AvailabilityModal.timeSlots.add(dayName);
            }

            function getLastEndTime(dayItem) {
                return AvailabilityModal.timeSlots.getLastEndTime(dayItem);
            }













            // Backward compatibility functions
            function getWeekKey(date) {
                return AvailabilityModal.weekManager.getWeekKey(date);
            }

            function saveCurrentWeekSelections() {
                AvailabilityModal.weekManager.saveCurrentWeekSelections();
            }

            function loadWeekSelections() {
                AvailabilityModal.weekManager.loadWeekSelections();
            }

            function loadSelectionsFromEditData(date) {
                return AvailabilityModal.weekManager.loadSelectionsFromEditData(date);
            }

            function resetFormToCleanState() {
                AvailabilityModal.weekManager.resetFormToCleanState();
            }

            function updateDateRange() {
                AvailabilityModal.weekManager.updateDateRange();
            }

            function getCurrentWeekMonday() {
                return AvailabilityModal.weekManager.getCurrentWeekMonday();
            }

            // Keep global currentStartDate in sync
            function getCurrentStartDate() {
                return AvailabilityModal.weekManager.currentStartDate || window.currentStartDate;
            }

            // Function to sync vacation dates between global and AvailabilityModal arrays
            function syncVacationDates() {
                selectedVacationDates = [...AvailabilityModal.selectedVacationDates];
                window.selectedVacationDates = selectedVacationDates;
            }



            // Event listeners for form changes
            $(document).on('change', '.day-toggle', function() {
                setTimeout(() => AvailabilityModal.weekManager.saveCurrentWeekSelections(), 100);
            });

            $(document).on('change', 'input[name="duration"]', function() {
                if (!$('input[name="recurring"]').is(':checked')) {
                    showToast('Please enable the recurring option first', 'warning');
                    $(this).prop('checked', false);
                    return;
                }
                handleDurationChange($(this).val());
            });

            $(document).on('change', 'input[name="recurring"]', function() {
                const isChecked = $(this).prop('checked');
                const $recurringOptions = $('.recurring-options');

                if (!isChecked) {
                    // When recurring is disabled, clear all duration selections
                    $('input[name="duration"]').prop('checked', false);
                    $('#custom-weeks').hide();
                    $('#custom-weeks-input').val('');

                    // Disable duration options visually
                    $recurringOptions.addClass('disabled');

                    showToast('Recurring schedule disabled. Duration selections cleared.', 'info');
                } else {
                    // Enable duration options visually
                    $recurringOptions.removeClass('disabled');
                }
            });
            $(document).on('change', '#custom-weeks-input', function() {
                setTimeout(() => AvailabilityModal.weekManager.saveCurrentWeekSelections(), 100);
            });

            $(document).on('input', '.timePicker', function() {
                setTimeout(() => AvailabilityModal.weekManager.saveCurrentWeekSelections(), 100);
            });


            // Day toggle functionality
            $(document).on('change', '.day-toggle', function() {
                const $checkbox = $(this);
                const $parent = $checkbox.closest(".day-item");
                const $timeInputGroup = $parent.find(".time-input-group");
                const $closedText = $parent.find(".closed-text");

                if ($checkbox.prop('checked')) {
                    $timeInputGroup.css('display', 'flex');
                    $closedText.hide();

                    const $timeInputs = $parent.find(".timePicker");
                    $timeInputs.removeAttr('readonly').prop('disabled', false);

                    console.log('Day checkbox checked, found time inputs:', $timeInputs.length);

                    // Initialize flatpickr if not already done
                    $timeInputs.each(function() {
                        console.log('Processing time input:', this, 'Has flatpickr:', !!this._flatpickr);
                        if (!this._flatpickr) {
                            console.log('Initializing flatpickr for time input...');
                            const fp = flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "h:i K",
                                time_24hr: false,
                                clickOpens: true,
                                allowInput: false,
                                minuteIncrement: 15,
                                defaultHour: $(this).hasClass('start-time') ? 10 : 19,
                                defaultMinute: 0
                            });
                            console.log('Flatpickr initialized:', fp);
                        }
                    });
                } else {
                    $timeInputGroup.hide();
                    $closedText.show();
                    $parent.find(".timePicker").attr('readonly', 'readonly').prop('disabled', true);
                }
            });



            // Initialize the modal with current week immediately
            if (!AvailabilityModal.weekManager.currentStartDate) {
                AvailabilityModal.weekManager.currentStartDate = AvailabilityModal.weekManager
                    .getCurrentWeekMonday();
            }
            window.currentStartDate = new Date(AvailabilityModal.weekManager
                .currentStartDate); // Keep global in sync
            AvailabilityModal.weekManager.updateDateRange();

            // Modal event handlers
            $('#availabilityModal').on('show.bs.modal', function() {
                AvailabilityModal.weekManager.currentStartDate = AvailabilityModal.weekManager
                    .getCurrentWeekMonday();
                window.currentStartDate = new Date(AvailabilityModal.weekManager
                    .currentStartDate); // Keep global in sync
                AvailabilityModal.weekManager.updateDateRange();

                // Initialize recurring options state
                const $recurringCheckbox = $('input[name="recurring"]');
                const $recurringOptions = $('.recurring-options');

                if (!$recurringCheckbox.is(':checked')) {
                    $recurringOptions.addClass('disabled');
                } else {
                    $recurringOptions.removeClass('disabled');
                }

                // Initialize flatpickr for all existing time inputs when modal is shown
                console.log('Modal shown, initializing flatpickr...');
                setTimeout(function() {
                    $('.timePicker').each(function() {
                        console.log('Found timePicker element:', this, 'Disabled:', this.disabled, 'Readonly:', this.readOnly);
                        if (!this._flatpickr) {
                            console.log('Initializing flatpickr for:', this);
                            // Temporarily enable the input for flatpickr initialization
                            const wasDisabled = this.disabled;
                            const wasReadonly = this.readOnly;
                            this.disabled = false;
                            this.readOnly = false;

                            const fp = flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "h:i K",
                                time_24hr: false,
                                clickOpens: true,
                                allowInput: false,
                                minuteIncrement: 15,
                                defaultHour: $(this).hasClass('start-time') || $(this).hasClass('additional-start-time') ?
                                    ($(this).hasClass('additional-start-time') ? 20 : 10) :
                                    ($(this).hasClass('additional-end-time') ? 22 : 19),
                                defaultMinute: 0
                            });

                            console.log('Flatpickr initialized:', fp);

                            // Restore original state if it was disabled/readonly
                            if (wasDisabled) this.disabled = true;
                            if (wasReadonly) this.readOnly = true;
                        }
                    });
                }, 100);

                // Add click event handler for debugging
                $(document).on('click', '.timePicker', function() {
                    console.log('TimePicker clicked:', this);
                    console.log('Has flatpickr:', !!this._flatpickr);
                    console.log('Is disabled:', this.disabled);
                    console.log('Is readonly:', this.readOnly);

                    // If flatpickr is not initialized, try to initialize it now
                    if (!this._flatpickr && !this.disabled && !this.readOnly) {
                        console.log('Initializing flatpickr on click...');
                        const fp = flatpickr(this, {
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "h:i K",
                            time_24hr: false,
                            clickOpens: true,
                            allowInput: false,
                            minuteIncrement: 15,
                            defaultHour: $(this).hasClass('start-time') || $(this).hasClass('additional-start-time') ?
                                ($(this).hasClass('additional-start-time') ? 20 : 10) :
                                ($(this).hasClass('additional-end-time') ? 22 : 19),
                            defaultMinute: 0
                        });
                        console.log('Flatpickr initialized on click:', fp);
                        // Open it immediately
                        if (fp) {
                            fp.open();
                        }
                    } else if (this._flatpickr) {
                        console.log('Opening existing flatpickr...');
                        this._flatpickr.open();
                    }
                });

                if (isEditMode && editAvailabilityData.length > 0) {
                    populateModalWithEditData();
                } else {
                    // In create mode, check if there's stored data in hidden inputs
                    populateModalWithStoredData();
                }

                setTimeout(() => AvailabilityModal.vacation.update(), 200);
            });

            // Reset modal when closed or cancelled
            function resetAvailabilityModal() {
                // Reset to current week
                AvailabilityModal.weekManager.currentStartDate = AvailabilityModal.weekManager
                    .getCurrentWeekMonday();
                window.currentStartDate = new Date(AvailabilityModal.weekManager.currentStartDate);
                AvailabilityModal.weekManager.updateDateRange();

                // Clear weekly selections (temporary changes)
                AvailabilityModal.weeklySelections = {};
                weeklySelections = {};

                // Reset vacation dates to original state if in edit mode
                if (isEditMode && editVacationsData.length > 0) {
                    AvailabilityModal.selectedVacationDates = [...editVacationsData];
                    selectedVacationDates = [...editVacationsData];
                } else if (!isEditMode) {
                    // Clear vacation dates if not in edit mode
                    AvailabilityModal.selectedVacationDates = [];
                    selectedVacationDates = [];
                }

                // Reset form to clean state
                AvailabilityModal.weekManager.resetFormToCleanState();

                // Reset recurring options
                $('input[name="recurring"]').prop('checked', false);
                $('input[name="duration"]').prop('checked', false);
                $('#custom-weeks-input').val('');
                $('#custom-weeks').hide();
                $('.recurring-options').addClass('disabled');

                // Update vacation display
                AvailabilityModal.vacation.update();

                // Repopulate with edit data if in edit mode
                if (isEditMode && editAvailabilityData.length > 0) {
                    setTimeout(() => populateModalWithEditData(), 100);
                }
            }

            // Handle cancel button click
            $('.cancel-btn').on('click', function() {
                resetAvailabilityModal();
            });

            // Handle modal close (X button or clicking outside)
            $('#availabilityModal').on('hidden.bs.modal', function() {
                resetAvailabilityModal();
            });

            // Apply button for custom weeks
            $("#apply-custom-weeks-btn").on('click', function() {
                const $recurringCheckbox = $('input[name="recurring"]');
                const $customRadio = $('input[name="duration"][value="custom"]');
                const $customWeeksInput = $("#custom-weeks-input");

                if (!$recurringCheckbox.is(':checked')) {
                    showToast('Please enable the recurring option first', 'warning');
                    return;
                }

                if (!$customRadio.prop('checked')) {
                    showToast('Please select the custom duration option first', 'warning');
                    return;
                }

                if (!$customWeeksInput.val()) {
                    showToast('Please enter the number of weeks first.', 'warning');
                    return;
                }

                handleDurationChange('custom');
            });

            // Handle duration selection and copy schedule to multiple weeks
            function handleDurationChange(selectedValue) {
                // Double-check that recurring is enabled
                if (!$('input[name="recurring"]').is(':checked')) {
                    showToast('Please enable the recurring option first', 'warning');
                    $(`input[name="duration"][value="${selectedValue}"]`).prop('checked', false);
                    return;
                }

                // Show/hide custom weeks input
                const $customWeeks = $("#custom-weeks");
                if (selectedValue === 'custom') {
                    $customWeeks.show();
                } else {
                    $customWeeks.hide();
                }
                // Get current week's selections
                const currentWeekSelections = getCurrentWeekSelections();

                // Check if user has selected any days
                const hasSelections = Object.values(currentWeekSelections.days).some(day => day.checked);


                if (!hasSelections) {
                    showToast('Please select at least one working day first, then choose the duration.',
                        'warning');

                    // Uncheck the radio button
                    $(`input[name="duration"][value="${selectedValue}"]`).prop('checked', false);
                    return;
                }

                // Determine number of weeks to copy
                let weeksCount;
                if (selectedValue === 'custom') {
                    const $customInput = $("#custom-weeks-input");
                    weeksCount = parseInt($customInput.val());
                    if (!weeksCount || weeksCount < 1) {
                        showToast('Please enter a valid number of weeks.', 'warning');
                        return;
                    }
                } else {
                    weeksCount = parseInt(selectedValue);
                }

                // Copy current week's schedule to the next weeks
                window.copyScheduleToMultipleWeeks(currentWeekSelections, weeksCount);

                // Show success message
                if (weeksCount === 1) {
                    showToast('Your schedule has been applied to the current week.', 'success');
                } else {
                    showToast(`Your schedule has been copied to the next ${weeksCount} weeks.`, 'success');
                }
            }

            function getCurrentWeekSelections() {
                const selections = {
                    days: {},
                    duration: null,
                    customWeeks: null,
                    recurring: false
                };

                // Get day selections
                $(".day-toggle").each(function() {
                    const $checkbox = $(this);
                    const $parent = $checkbox.closest(".day-item");
                    if ($parent.length) {
                        const dayName = $parent.attr("data-day");
                        const $startTimeInput = $parent.find(".start-time");
                        const $endTimeInput = $parent.find(".end-time");
                        const isChecked = $checkbox.prop('checked');

                        if (isChecked) {
                            selections.days[dayName] = {
                                checked: true,
                                enabled: true, // For compatibility
                                startTime: $startTimeInput.length ? $startTimeInput.val() : "10:00 AM",
                                endTime: $endTimeInput.length ? $endTimeInput.val() : "7:00 PM",
                                additionalSlots: []
                            };

                            // Collect additional time slots
                            $parent.find('.additional-time-slot').each(function() {
                                const $slot = $(this);
                                const additionalStart = $slot.find('.additional-start-time').val();
                                const additionalEnd = $slot.find('.additional-end-time').val();
                                if (additionalStart && additionalEnd) {
                                    selections.days[dayName].additionalSlots.push({
                                        startTime: additionalStart,
                                        endTime: additionalEnd
                                    });
                                }
                            });
                        } else {
                            selections.days[dayName] = {
                                checked: false,
                                enabled: false,
                                startTime: "10:00 AM",
                                endTime: "7:00 PM",
                                additionalSlots: []
                            };
                        }
                    }
                });

                // Get recurring and duration selection
                selections.recurring = $("input[name='recurring']").is(':checked');

                const $selectedDuration = $("input[name='duration']:checked");
                if ($selectedDuration.length) {
                    selections.duration = $selectedDuration.val();
                }

                const $customWeeksInput = $("#custom-weeks-input");
                if ($customWeeksInput.length) {
                    selections.customWeeks = $customWeeksInput.val();
                }

                return selections;
            }
        });


        $(document).ready(function() {
            // Initialize time validation for all existing time inputs
            function initializeTimeInputs() {
                const $timeInputs = $('.time-input');
                addTimeValidation($timeInputs);
            }
            // Initialize on page load
            initializeTimeInputs();
            // Re-initialize when checkboxes are toggled
            $(document).on('change', '.day-toggle', function() {
                setTimeout(initializeTimeInputs, 100);
            });
        });
    </script>

    <script>
        // Global variables (some already declared above)
        let scheduleData = [];
        let vacationCalendar = null;
        let currentWeekStart = new Date();
        let currentStartDate = new Date(); // For backward compatibility
        let isEditMode = false;
        let editAvailabilityData = [];
        let editVacationsData = [];
        let editRecurringData = {};

        // Global validation functions
        function parseTime(timeStr) {
            if (!timeStr) return null;

            const timeRegex = /^(\d{1,2}):(\d{2})\s*(AM|PM)$/i;
            const match = timeStr.trim().match(timeRegex);

            if (!match) return null;

            let hours = parseInt(match[1]);
            const minutes = parseInt(match[2]);
            const period = match[3].toUpperCase();

            if (hours < 1 || hours > 12 || minutes < 0 || minutes > 59) return null;

            if (period === 'PM' && hours !== 12) hours += 12;
            if (period === 'AM' && hours === 12) hours = 0;

            return hours * 60 + minutes; // Return total minutes
        }

        function formatTime(minutes) {
            const hours = Math.floor(minutes / 60);
            const mins = minutes % 60;
            const period = hours >= 12 ? 'PM' : 'AM';
            const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
            return `${displayHours}:${mins.toString().padStart(2, '0')} ${period}`;
        }

        function showTimeError(input, message) {
            // Remove existing error message
            $(input).parent().find('.time-error').remove();

            // Add new error message
            const $errorDiv = $('<div>', {
                class: 'time-error text-danger fs-11 mt-1',
                text: message
            });
            $(input).parent().append($errorDiv);

            // Remove error after 5 seconds
            setTimeout(() => {
                $errorDiv.remove();
            }, 5000);
        }

        function validateTimeSlot(startInput, endInput) {
            const startTime = $(startInput).val().trim();
            const endTime = $(endInput).val().trim();

            // Clear previous errors
            $(startInput).removeClass('is-invalid');
            $(endInput).removeClass('is-invalid');

            if (!startTime || !endTime) return true; // Allow empty for now

            const startMinutes = parseTime(startTime);
            const endMinutes = parseTime(endTime);

            let hasError = false;

            // Validate format
            if (!startMinutes && startMinutes !== 0) {
                $(startInput).addClass('is-invalid');
                showTimeError(startInput, 'Invalid time format. Use format like "10:00 AM"');
                hasError = true;
            }

            if (!endMinutes && endMinutes !== 0) {
                $(endInput).addClass('is-invalid');
                showTimeError(endInput, 'Invalid time format. Use format like "7:00 PM"');
                hasError = true;
            }

            // Validate end time is after start time
            if (startMinutes !== null && endMinutes !== null && endMinutes <= startMinutes) {
                $(endInput).addClass('is-invalid');
                showTimeError(endInput, 'End time must be after start time');
                hasError = true;
            }

            return !hasError;
        }

        function validateTimeSequence(dayItem) {
            const timeSlots = [];
            const $dayItem = $(dayItem);

            // Get main time slot
            const $mainStart = $dayItem.find('.start-time');
            const $mainEnd = $dayItem.find('.end-time');
            if ($mainStart.val() && $mainEnd.val()) {
                timeSlots.push({
                    start: parseTime($mainStart.val()),
                    end: parseTime($mainEnd.val()),
                    startInput: $mainStart[0],
                    endInput: $mainEnd[0],
                    type: 'main'
                });
            }

            // Get additional time slots
            $dayItem.find('.additional-time-slot').each(function() {
                const $slot = $(this);
                const $startInput = $slot.find('.additional-start-time');
                const $endInput = $slot.find('.additional-end-time');
                if ($startInput.val() && $endInput.val()) {
                    timeSlots.push({
                        start: parseTime($startInput.val()),
                        end: parseTime($endInput.val()),
                        startInput: $startInput[0],
                        endInput: $endInput[0],
                        type: 'additional'
                    });
                }
            });

            // Sort by start time
            timeSlots.sort((a, b) => a.start - b.start);

            // Validate sequence
            let hasError = false;
            for (let i = 1; i < timeSlots.length; i++) {
                const prevSlot = timeSlots[i - 1];
                const currentSlot = timeSlots[i];

                if (currentSlot.start < prevSlot.end) {
                    $(currentSlot.startInput).addClass('is-invalid');
                    showTimeError(currentSlot.startInput,
                        'Time slots cannot overlap. Start time must be after previous end time.');
                    hasError = true;
                }
            }
            return !hasError;
        }

        function validateAllTimeInputs() {
            let hasErrors = false;
            const errorMessages = [];
            // Check all checked days for time validation
            $('.day-item').each(function() {
                const $dayItem = $(this);
                const $checkbox = $dayItem.find('.day-toggle');
                if ($checkbox.length && $checkbox.prop('checked')) {
                    const dayName = $dayItem.attr('data-day');
                    // Validate main time slot
                    const $startInput = $dayItem.find('.start-time');
                    const $endInput = $dayItem.find('.end-time');
                    if (!$startInput.val().trim() || !$endInput.val().trim()) {
                        errorMessages.push(`${dayName}: Please enter both start and end times.`);
                        hasErrors = true;
                    } else {
                        // Validate format and sequence
                        if (!validateTimeSlot($startInput[0], $endInput[0])) {
                            hasErrors = true;
                        }
                    }
                    // Validate additional time slots
                    $dayItem.find('.additional-time-slot').each(function() {
                        const $slot = $(this);
                        const $additionalStart = $slot.find('.additional-start-time');
                        const $additionalEnd = $slot.find('.additional-end-time');
                        if (!$additionalStart.val().trim() || !$additionalEnd.val().trim()) {
                            errorMessages.push(
                                `${dayName}: Please enter times for all additional slots or remove empty ones.`
                            );
                            hasErrors = true;
                        } else {
                            if (!validateTimeSlot($additionalStart[0], $additionalEnd[0])) {
                                hasErrors = true;
                            }
                        }
                    });
                    // Validate time sequence for this day
                    if (!validateTimeSequence(this)) {
                        hasErrors = true;
                    }
                }
            });
            if (hasErrors) {
                if (errorMessages.length > 0) {
                    alert("Please fix the following errors:\n\n" + errorMessages.join('\n'));
                } else {
                    alert("Please fix the time validation errors before saving.");
                }
                return false;
            }
            return true;
        }
        // Global functions for edit mode
        window.setEditAvailabilityData = function(availabilityData, vacationsData, recurringData) {
            console.log('Setting edit data:', {
                availabilityData,
                vacationsData,
                recurringData
            });

            isEditMode = true;
            editAvailabilityData = availabilityData || [];
            editVacationsData = vacationsData || [];
            editRecurringData = recurringData || {};
            selectedVacationDates = vacationsData || [];

            // Sync with AvailabilityModal
            AvailabilityModal.selectedVacationDates = [...(vacationsData || [])];

            // Update vacation display
            AvailabilityModal.vacation.update();
        };
        window.clearEditAvailabilityData = function() {
            isEditMode = false;
            editAvailabilityData = [];
            editVacationsData = [];
            editRecurringData = {};
            selectedVacationDates = [];
        };

        // Function to populate modal with stored data from hidden inputs (create mode)
        function populateModalWithStoredData() {
            // Check if there's stored availability data
            const $availabilityData = $('#availability-data');
            const $vacationsData = $('#vacations-data');
            const $recurringData = $('#recurring-data');

            if (!$availabilityData.length) return; // Not in create mode

            const availabilityDataValue = $availabilityData.val();
            const vacationsDataValue = $vacationsData.val();
            const recurringDataValue = $recurringData.val();

            // If no stored data, return
            if (!availabilityDataValue && !vacationsDataValue && !recurringDataValue) {
                return;
            }



            try {
                // Parse stored data
                const storedAvailability = availabilityDataValue ? JSON.parse(availabilityDataValue) : [];
                const storedVacations = vacationsDataValue ? JSON.parse(vacationsDataValue) : [];
                const storedRecurring = recurringDataValue ? JSON.parse(recurringDataValue) : {};

                // Populate form with stored availability data
                if (storedAvailability.length > 0) {
                    // Clear existing selections first
                    $('.day-item').each(function() {
                        const $dayItem = $(this);
                        const $checkbox = $dayItem.find('.day-toggle');
                        $checkbox.prop('checked', false);
                        $dayItem.removeClass('selected');
                    });

                    // Set stored selections
                    storedAvailability.forEach(dayData => {
                        const $dayItem = $(`[data-day="${dayData.day}"]`);
                        if ($dayItem.length) {
                            const $checkbox = $dayItem.find('.day-toggle');
                            $checkbox.prop('checked', true);
                            $dayItem.addClass('selected');

                            // Set times
                            if (dayData.start_time) {
                                $dayItem.find('.start-time').val(dayData.start_time);
                            }
                            if (dayData.end_time) {
                                $dayItem.find('.end-time').val(dayData.end_time);
                            }

                            // Add additional time slots if any
                            if (dayData.additional_slots && dayData.additional_slots.length > 0) {
                                const $additionalSlotsContainer = $dayItem.find('.additional-time-slots');
                                if ($additionalSlotsContainer.length) {
                                    $additionalSlotsContainer.empty();

                                    dayData.additional_slots.forEach((slot, index) => {
                                        const slotId = Date.now() + index;
                                        const slotHtml = `
                                            <div class="additional-time-slot" data-slot-id="${slotId}">
                                                <input class="time-input additional-start-time timePicker" type="text"
                                                       placeholder="e.g. 8:00 PM" value="${slot.start_time}" />
                                                <span class="text-muted">to</span>
                                                <input class="time-input additional-end-time timePicker" type="text"
                                                       placeholder="e.g. 10:00 PM" value="${slot.end_time}" />
                                                <button type="button" class="remove-time-slot-btn"
                                                        onclick="AvailabilityModal.timeSlots.remove('${dayData.day}', '${slotId}')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        `;
                                        $additionalSlotsContainer.append(slotHtml);
                                    });

                                    // Initialize flatpickr for all newly added time inputs
                                    $additionalSlotsContainer.find('.timePicker').each(function() {
                                        if (!this._flatpickr) {
                                            flatpickr(this, {
                                                enableTime: true,
                                                noCalendar: true,
                                                dateFormat: "h:i K",
                                                time_24hr: false,
                                                clickOpens: true,
                                                allowInput: false,
                                                minuteIncrement: 15,
                                                defaultHour: $(this).hasClass('additional-start-time') ? 20 : 22,
                                                defaultMinute: 0
                                            });
                                        }
                                    });
                                }
                            }

                            // Trigger change event to ensure UI updates
                            $checkbox.trigger('change');
                        }
                    });
                }

                // Set vacation dates
                if (storedVacations.length > 0) {
                    AvailabilityModal.selectedVacationDates = [...storedVacations];
                    selectedVacationDates = [...storedVacations];
                    AvailabilityModal.vacation.update();
                }

                // Set recurring options
                if (storedRecurring.recurring && storedRecurring.duration) {
                    const $durationRadio = $(`input[name="duration"][value="${storedRecurring.duration}"]`);
                    if ($durationRadio.length) {
                        $durationRadio.prop('checked', true);

                        if (storedRecurring.duration === 'custom' && storedRecurring.customWeeks) {
                            const $customWeeksInput = $('#custom-weeks-input');
                            const $customWeeksContainer = $('#custom-weeks');
                            if ($customWeeksInput.length) {
                                $customWeeksInput.val(storedRecurring.customWeeks);
                            }
                            if ($customWeeksContainer.length) {
                                $customWeeksContainer.show();
                            }
                        }
                    }
                }

                // Auto-copy schedule to multiple weeks if recurring is set
                if (storedRecurring.recurring && storedRecurring.duration && storedAvailability.length > 0) {
                    setTimeout(() => {
                        // Create current week selections from stored data
                        const currentWeekSelections = {
                            days: {},
                            duration: storedRecurring.duration,
                            customWeeks: storedRecurring.customWeeks
                        };

                        // Convert stored availability to the format expected by copyScheduleToMultipleWeeks
                        storedAvailability.forEach(dayData => {
                            currentWeekSelections.days[dayData.day] = {
                                checked: true,
                                enabled: true,
                                startTime: dayData.start_time,
                                endTime: dayData.end_time,
                                additionalSlots: dayData.additional_slots || []
                            };
                        });

                        // Add unchecked days
                        const allDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday',
                            'sunday'
                        ];
                        allDays.forEach(day => {
                            if (!currentWeekSelections.days[day]) {
                                currentWeekSelections.days[day] = {
                                    checked: false,
                                    enabled: false,
                                    startTime: "10:00 AM",
                                    endTime: "7:00 PM",
                                    additionalSlots: []
                                };
                            }
                        });

                        let weeksCount;
                        if (storedRecurring.duration === 'custom') {
                            weeksCount = parseInt(storedRecurring.customWeeks) || 1;
                        } else {
                            weeksCount = parseInt(storedRecurring.duration) || 1;
                        }

                        if (weeksCount > 1) {
                            window.copyScheduleToMultipleWeeks(currentWeekSelections, weeksCount);
                        }
                    }, 500); // Delay to ensure form is fully populated
                }

            } catch (error) {
                // Silently handle errors
            }
        }

        function populateModalWithEditData() {
            // Clear existing selections first
            $('.day-item').each(function() {
                const $dayItem = $(this);
                const $checkbox = $dayItem.find('.day-toggle');
                const dayName = $dayItem.attr('data-day');

                // Find if this day has availability data (check all entries for this day)
                const dayData = editAvailabilityData.find(item => item.day === dayName);
                if (dayData) {
                    // Check the day
                    $checkbox.prop('checked', true);
                    $dayItem.addClass('selected');
                    // Set main time slot
                    if (dayData.main_slot) {
                        const $startInput = $dayItem.find('.start-time');
                        const $endInput = $dayItem.find('.end-time');
                        if ($startInput.length) $startInput.val(dayData.main_slot.start_time);
                        if ($endInput.length) $endInput.val(dayData.main_slot.end_time);
                    }
                    // Add additional time slots
                    if (dayData.additional_slots && dayData.additional_slots.length > 0) {
                        const $additionalSlotsContainer = $dayItem.find('.additional-time-slots');
                        if ($additionalSlotsContainer.length) {
                            $additionalSlotsContainer.empty(); // Clear existing

                            dayData.additional_slots.forEach((slot, index) => {
                                const slotId = Date.now() + index;
                                const slotHtml = `
                                    <div class="additional-time-slot" data-slot-id="${slotId}">
                                        <input class="time-input additional-start-time timePicker" type="text" value="${slot.start_time}" />
                                        <span class="text-muted">to</span>
                                        <input class="time-input additional-end-time timePicker" type="text" value="${slot.end_time}" />
                                        <button type="button" class="remove-time-slot-btn" onclick="removeTimeSlot('${dayName}', '${slotId}')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                `;
                                $additionalSlotsContainer.append(slotHtml);
                            });

                            // Initialize flatpickr for all newly added time inputs
                            $additionalSlotsContainer.find('.timePicker').each(function() {
                                if (!this._flatpickr) {
                                    flatpickr(this, {
                                        enableTime: true,
                                        noCalendar: true,
                                        dateFormat: "h:i K",
                                        time_24hr: false,
                                        clickOpens: true,
                                        allowInput: false,
                                        minuteIncrement: 15,
                                        defaultHour: $(this).hasClass('additional-start-time') ? 20 : 22,
                                        defaultMinute: 0
                                    });
                                }
                            });
                        }
                    }
                    // Show time inputs and hide closed text
                    const $timeInputGroup = $dayItem.find('.time-input-group');
                    const $closedText = $dayItem.find('.closed-text');

                    if ($timeInputGroup.length) $timeInputGroup.css('display', 'flex');
                    if ($closedText.length) $closedText.hide();

                    // Enable time inputs
                    const $timeInputs = $dayItem.find('.timePicker');
                    $timeInputs.removeAttr('readonly').prop('disabled', false);

                    // Trigger change event to ensure UI updates
                    $checkbox.trigger('change');
                } else {
                    // Uncheck the day
                    $checkbox.prop('checked', false);
                    $dayItem.removeClass('selected');
                }
            });
            // Set recurring options if available
            if (editRecurringData && editRecurringData.recurring && editRecurringData.duration) {
                // Set duration radio button
                const $durationRadio = $(`input[name="duration"][value="${editRecurringData.duration}"]`);
                if ($durationRadio.length) {
                    $durationRadio.prop('checked', true);
                    // If custom, set the custom weeks value and show the input
                    if (editRecurringData.duration === 'custom' && editRecurringData.customWeeks) {
                        const $customWeeksInput = $('#custom-weeks-input');
                        const $customWeeksContainer = $('#custom-weeks');
                        if ($customWeeksInput.length) {
                            $customWeeksInput.val(editRecurringData.customWeeks);
                        }
                        if ($customWeeksContainer.length) {
                            $customWeeksContainer.show();
                        }
                    }
                }
            }
            // Update vacation display
            if (editVacationsData.length > 0) {
                selectedVacationDates = [...editVacationsData];
                // Use setTimeout to ensure DOM elements are available
                setTimeout(() => {
                    updateVacationDisplay();
                }, 100);
            }
            // Set recurring options if available
            if (editRecurringData && editRecurringData.recurring && editRecurringData.duration) {
                // Set duration radio button
                const $durationRadio = $(`input[name="duration"][value="${editRecurringData.duration}"]`);
                if ($durationRadio.length) {
                    $durationRadio.prop('checked', true);
                    // If custom, set the custom weeks value and show the input
                    if (editRecurringData.duration === 'custom' && editRecurringData.customWeeks) {
                        const $customWeeksInput = $('#custom-weeks-input');
                        const $customWeeksContainer = $('#custom-weeks');
                        if ($customWeeksInput.length) {
                            $customWeeksInput.val(editRecurringData.customWeeks);
                        }
                        if ($customWeeksContainer.length) {
                            $customWeeksContainer.show();
                        }
                    }

                    // Automatically copy the schedule to multiple weeks in edit mode
                    setTimeout(() => {
                        // Create current week selections directly since getCurrentWeekSelections is not in scope
                        const currentWeekSelections = {
                            days: {},
                            duration: editRecurringData.duration,
                            customWeeks: editRecurringData.customWeeks
                        };

                        // Get day selections from the form
                        $(".day-toggle").each(function() {
                            const $checkbox = $(this);
                            const $parent = $checkbox.closest(".day-item");
                            if ($parent.length) {
                                const dayName = $parent.attr("data-day");
                                const $startTimeInput = $parent.find(".start-time");
                                const $endTimeInput = $parent.find(".end-time");
                                const isChecked = $checkbox.prop('checked');

                                if (isChecked) {
                                    currentWeekSelections.days[dayName] = {
                                        checked: true,
                                        enabled: true,
                                        startTime: $startTimeInput.length ? $startTimeInput.val() :
                                            "10:00 AM",
                                        endTime: $endTimeInput.length ? $endTimeInput.val() : "7:00 PM",
                                        additionalSlots: []
                                    };
                                } else {
                                    currentWeekSelections.days[dayName] = {
                                        checked: false,
                                        enabled: false,
                                        startTime: "10:00 AM",
                                        endTime: "7:00 PM",
                                        additionalSlots: []
                                    };
                                }
                            }
                        });

                        const hasSelections = Object.values(currentWeekSelections.days).some(day => day.checked);

                        if (hasSelections) {
                            let weeksCount;
                            if (editRecurringData.duration === 'custom') {
                                weeksCount = parseInt(editRecurringData.customWeeks) || 1;
                            } else {
                                weeksCount = parseInt(editRecurringData.duration) || 1;
                            }

                            if (weeksCount > 1) {
                                window.copyScheduleToMultipleWeeks(currentWeekSelections, weeksCount);
                            }
                        }
                    }, 300); // Small delay to ensure form is fully populated
                }
            }
        }

        $(document).ready(function() {
            function displayAvailableDates(selectedDays) {
                const weekStart = window.currentWeekStart || new Date();
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                let html = '';

                selectedDays.forEach(dayName => {
                    const dayIndex = dayNames.indexOf(dayName);
                    const date = new Date(weekStart);
                    date.setDate(weekStart.getDate() + dayIndex);

                    const dateStr = date.toISOString().split('T')[0];
                    const formattedDate = date.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'short',
                        day: 'numeric'
                    });

                    html += `
                        <div class="date-card" data-date="${dateStr}" data-day="${dayName}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${formattedDate}</strong>
                                    <div class="fs-12 text-muted">Click to add breaks</div>
                                </div>
                                <div class="break-count">
                                    <span class="badge bg-secondary" id="break-count-${dateStr}">0 breaks</span>
                                </div>
                            </div>
                            <div class="break-time-inputs" id="breaks-${dateStr}">
                                <button type="button" class="add-break-btn" onclick="addBreakTime('${dateStr}')">
                                    <i class="fas fa-plus me-1"></i> Add Break
                                </button>
                            </div>
                        </div>
                    `;
                });
                availableDatesList.innerHTML = html;
                // Add click handlers for date cards
                $('.date-card').each(function() {
                    $(this).on('click', function() {
                        const $breakInputs = $(this).find('.break-time-inputs');
                        if ($breakInputs.hasClass('show')) {
                            $breakInputs.removeClass('show');
                            $(this).removeClass('selected');
                        } else {
                            // Hide all other break inputs
                            $('.break-time-inputs').removeClass('show');
                            $('.date-card').removeClass('selected');

                            // Show this one
                            $breakInputs.addClass('show');
                            $(this).addClass('selected');
                        }
                    });
                });
            }

            // Generate schedule functionality (for recurring schedules)
            const $generateScheduleBtn = $("#generate-schedule-btn");
            const $schedulePreview = $("#schedule-preview");
            const $scheduleDatesContainer = $("#schedule-dates-container");

            if ($generateScheduleBtn.length) {
                $generateScheduleBtn.on("click", function() {
                    // Save current week selections first
                    AvailabilityModal.weekManager.saveCurrentWeekSelections();

                    const recurringData = collectRecurringData();
                    const weeksToGenerate = calculateWeeks(recurringData);

                    // For recuring schedules, collect data from all saved weeks
                    const allWeeksData = collectAllWeeksData(weeksToGenerate);
                    if (allWeeksData.length === 0) {
                        showToast('Please select at least one working day first.', 'warning');
                        return;
                    }

                    scheduleData = generateRecurringSchedule(allWeeksData, weeksToGenerate);
                    displaySchedulePreview(scheduleData);
                    $schedulePreview.show();
                });
            }

            function collectBasicAvailabilityData() {
                const data = {
                    days: []
                };
                $(".day-item").each(function() {
                    const $dayElement = $(this);
                    const $checkbox = $dayElement.find(".day-toggle");
                    if ($checkbox.length && $checkbox.prop('checked')) {
                        const dayName = $dayElement.attr("data-day");
                        const $startTimeInput = $dayElement.find(".start-time");
                        const $endTimeInput = $dayElement.find(".end-time");

                        const startTime = $startTimeInput.length ? $startTimeInput.val() : "10:00 AM";
                        const endTime = $endTimeInput.length ? $endTimeInput.val() : "7:00 PM";

                        // Collect additional time slots
                        const additionalSlots = [];
                        $dayElement.find('.additional-time-slot').each(function() {
                            const $slot = $(this);
                            const $additionalStart = $slot.find('.additional-start-time');
                            const $additionalEnd = $slot.find('.additional-end-time');

                            if ($additionalStart.length && $additionalEnd.length) {
                                additionalSlots.push({
                                    start_time: $additionalStart.val(),
                                    end_time: $additionalEnd.val()
                                });
                            }
                        });
                        data.days.push({
                            day: dayName,
                            start_time: startTime,
                            end_time: endTime,
                            additional_slots: additionalSlots
                        });
                    }
                });
                return data;
            }

            function collectRecurringData() {
                const $selectedDuration = $("input[name='duration']:checked");
                return {
                    recurring: $selectedDuration.length > 0, // true if any duration is selected
                    duration: $selectedDuration.length ? $selectedDuration.val() : null,
                    customWeeks: $("#custom-weeks-input").length ? $("#custom-weeks-input").val() : null
                };
            }
            // Generate recurring schedule from all weeks data
            function generateRecurringSchedule(allWeeksData, weeksToGenerate) {
                const schedule = [];
                const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                allWeeksData.forEach(weekData => {
                    const dayIndex = dayNames.indexOf(weekData.day);
                    const currentDate = new Date(weekData.weekStart);
                    currentDate.setDate(weekData.weekStart.getDate() + dayIndex);
                    // Skip vacation dates
                    const dateStr = currentDate.toISOString().split('T')[0];
                    if (!selectedVacationDates.includes(dateStr)) {
                        // Add main time slot
                        schedule.push({
                            date: dateStr,
                            day: weekData.day,
                            start_time: weekData.start_time,
                            end_time: weekData.end_time,
                            week: weekData.week,
                            breaks: [],
                            slot_type: 'main'
                        });
                        // Add additional time slots if they exist
                        if (weekData.additional_slots && weekData.additional_slots.length > 0) {
                            weekData.additional_slots.forEach((slot, index) => {
                                schedule.push({
                                    date: dateStr,
                                    day: weekData.day,
                                    start_time: slot.startTime,
                                    end_time: slot.endTime,
                                    week: weekData.week,
                                    breaks: [],
                                    slot_type: 'additional',
                                    slot_index: index + 1
                                });
                            });
                        }
                    }
                });
                return schedule;
            }

            function generateScheduleDates(days, weeks) {
                const schedule = [];
                const startDate = window.currentWeekStart || new Date();
                // Ensure we start from Monday of the current displayed week
                const baseDate = new Date(startDate);
                const dayOfWeek = baseDate.getDay();
                const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
                baseDate.setDate(baseDate.getDate() + daysToMonday);

                for (let week = 0; week < weeks; week++) {
                    days.forEach(dayData => {
                        const dayOfWeek = getDayOfWeekNumber(dayData.day);
                        const currentDate = new Date(baseDate);
                        currentDate.setDate(baseDate.getDate() + (week * 7) + dayOfWeek);

                        // Skip vacation dates
                        const dateStr = currentDate.toISOString().split('T')[0];
                        if (!selectedVacationDates.includes(dateStr)) {
                            // Add main time slot
                            schedule.push({
                                date: dateStr,
                                day: dayData.day,
                                start_time: dayData.start_time,
                                end_time: dayData.end_time,
                                week: week + 1,
                                breaks: [],
                                slot_type: 'main'
                            });

                            // Add additional time slots if they exist
                            if (dayData.additional_slots && dayData.additional_slots.length > 0) {
                                dayData.additional_slots.forEach((slot, index) => {
                                    schedule.push({
                                        date: dateStr,
                                        day: dayData.day,
                                        start_time: slot.start_time,
                                        end_time: slot.end_time,
                                        week: week + 1,
                                        breaks: [],
                                        slot_type: 'additional',
                                        slot_index: index + 1
                                    });
                                });
                            }
                        }
                    });
                }
                return schedule;
            }

            function getDayOfWeekNumber(dayName) {
                const days = {
                    'Monday': 0,
                    'Tuesday': 1,
                    'Wednesday': 2,
                    'Thursday': 3,
                    'Friday': 4,
                    'Saturday': 5,
                    'Sunday': 6
                };
                return days[dayName] || 0;
            }

            function displaySchedulePreview(schedule) {
                let html = '';
                // Group by week, then by date
                const weekGroups = {};
                schedule.forEach(item => {
                    if (!weekGroups[item.week]) {
                        weekGroups[item.week] = {};
                    }
                    if (!weekGroups[item.week][item.date]) {
                        weekGroups[item.week][item.date] = [];
                    }
                    weekGroups[item.week][item.date].push(item);
                });

                Object.keys(weekGroups).forEach(weekNum => {
                    html += `<div class="week-group mb-4">`;
                    html += `<h6 class="fs-13 semi-bold text-primary mb-3">Week ${weekNum}</h6>`;

                    Object.keys(weekGroups[weekNum]).forEach(date => {
                        const dateItems = weekGroups[weekNum][date];
                        // Parse date string manually to avoid timezone issues
                        const [year, month, day] = date.split('-').map(Number);
                        const dateObj = new Date(year, month - 1, day); // month is 0-indexed
                        const formattedDate = dateObj.toLocaleDateString('en-US', {
                            weekday: 'long',
                            month: 'short',
                            day: 'numeric'
                        });
                        html += `
                            <div class="schedule-item mb-2 p-3 border rounded" data-date="${date}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>${formattedDate}</strong>
                        `;
                        // Display all time slots for this date
                        dateItems.forEach((item, index) => {
                            const slotLabel = item.slot_type === 'additional' ?
                                ` (Slot ${item.slot_index + 1})` : '';
                            html +=
                                `<div class="fs-12 text-muted">${item.start_time} - ${item.end_time}${slotLabel}</div>`;
                        });
                        html += `
                                    </div>
                                    <div class="text-end">
                                        <div class="badge bg-success fs-10">Working Day</div>
                                        ${dateItems.length > 1 ? `<div class="badge bg-info fs-10 mt-1">${dateItems.length} Time Slots</div>` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += `</div>`;
                });
                if (schedule.length === 0) {
                    html =
                        '<div class="text-center text-muted py-4">No schedule generated. Please select working days and recurring options.</div>';
                }
                $scheduleDatesContainer.html(html);
            }
            window.getScheduleData = function() {
                return scheduleData;
            };
        });
        // Vacation calendar functionality
        $(document).ready(function() {
            const $vacationCalendarBtn = $("#vacation-calendar-btn");
            const vacationCalendarModal = new bootstrap.Modal($('#vacationCalendarModal')[0]);
            const $confirmVacationBtn = $("#confirm-vacation-dates");
            const $vacationDatesList = $("#vacation-dates-list");
            const $vacationDatesInput = $("#vacation-dates");
            // Open vacation calendar modal
            $vacationCalendarBtn.on('click', function() {
                // Sync vacation dates before opening modal
                selectedVacationDates = [...AvailabilityModal.selectedVacationDates];
                vacationCalendarModal.show();
                initializeVacationCalendar();
            });

            function initializeVacationCalendar() {
                // Destroy existing calendar if it exists
                if (vacationCalendar) {
                    vacationCalendar.destroy();
                }

                // Initialize flatpickr calendar
                vacationCalendar = flatpickr("#vacation-calendar", {
                    mode: "multiple",
                    inline: true,
                    dateFormat: "Y-m-d",
                    minDate: "today",
                    defaultDate: selectedVacationDates,
                    onChange: function(selectedDates, dateStr, instance) {
                        updateVacationPreview(selectedDates);
                    }
                });
            }

            function updateVacationPreview(selectedDates) {
                const $vacationPreview = $("#vacation-preview");
                if (selectedDates.length === 0) {
                    $vacationPreview.html('<span class="text-muted fs-12">No dates selected</span>');
                } else {
                    const dateElements = selectedDates.map((date, index) => {
                        // Create a new date in local timezone to avoid timezone shifts
                        const localDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
                        const formattedDate = localDate.toLocaleDateString('en-US', {
                            weekday: 'short',
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                        });
                        return `
                            <div class="vacation-badge">
                                ${formattedDate}
                                <button type="button" class="btn-close btn-close-white" onclick="removeVacationDateFromPreview(${index})"></button>
                            </div>
                        `;
                    });
                    $vacationPreview.html(dateElements.join(''));
                }
            }
            // Function to remove vacation date from preview
            window.removeVacationDateFromPreview = function(index) {
                if (vacationCalendar && vacationCalendar.selectedDates[index]) {
                    const dateToRemove = vacationCalendar.selectedDates[index];
                    const newSelectedDates = vacationCalendar.selectedDates.filter((date, i) => i !== index);
                    vacationCalendar.setDate(newSelectedDates);
                    updateVacationPreview(newSelectedDates);
                }
            };
            // Confirm vacation dates selection
            $confirmVacationBtn.on('click', function() {
                if (vacationCalendar) {
                    selectedVacationDates = vacationCalendar.selectedDates.map(date => {
                        // Format date as YYYY-MM-DD without timezone conversion
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        return `${year}-${month}-${day}`;
                    });

                    // Update both global and AvailabilityModal arrays
                    AvailabilityModal.selectedVacationDates = [...selectedVacationDates];

                    updateVacationDisplay();
                    AvailabilityModal.vacation.update(); // Also update the AvailabilityModal display
                    vacationCalendarModal.hide();
                }
            });

            // Handle cancel button and modal close - reset to previously saved dates
            function resetVacationCalendar() {
                if (vacationCalendar) {
                    // Reset to the previously confirmed vacation dates
                    const previousDates = AvailabilityModal.selectedVacationDates.map(dateStr => {
                        const [year, month, day] = dateStr.split('-').map(Number);
                        return new Date(year, month - 1, day);
                    });
                    vacationCalendar.setDate(previousDates);
                    updateVacationPreview(previousDates);
                }
            }

            // Cancel button handler
            $('#vacationCalendarModal .btn-outline-custom').on('click', function() {
                resetVacationCalendar();
                vacationCalendarModal.hide();
            });

            // Modal close event handler (X button or clicking outside)
            $('#vacationCalendarModal').on('hidden.bs.modal', function() {
                resetVacationCalendar();
            });

            // Make vacation dates accessible globally
            window.getSelectedVacationDates = function() {
                return selectedVacationDates;
            };

            // Test function to add vacation dates (for debugging)
            window.testAddVacationDate = function(dateStr) {
                if (!dateStr) {
                    dateStr = new Date().toISOString().split('T')[0]; // Today's date
                }
                AvailabilityModal.selectedVacationDates.push(dateStr);
                selectedVacationDates.push(dateStr);
                console.log('Test vacation date added:', dateStr);
                AvailabilityModal.vacation.update();
            };
        });

        // Global functions that need to be accessible from multiple $(document).ready blocks
        function calculateWeeks(recurringData) {
            if (!recurringData.recurring) return 1;

            switch (recurringData.duration) {
                case '4':
                    return 4;
                case '8':
                    return 8;
                case 'custom':
                    return parseInt(recurringData.customWeeks) || 1;
                default:
                    return 1;
            }
        }

        // Collect data from all saved weeks for recurring schedule generation
        function collectAllWeeksData(weeksToGenerate) {
            const allWeeksData = [];
            const baseWeekStart = new Date(currentStartDate);

            for (let weekOffset = 0; weekOffset < weeksToGenerate; weekOffset++) {
                const weekStart = new Date(baseWeekStart);
                weekStart.setDate(baseWeekStart.getDate() + (weekOffset * 7));
                const weekKey = AvailabilityModal.weekManager.getWeekKey(weekStart);
                // Get saved selections for this week
                const weekSelections = weeklySelections[weekKey];

                if (weekSelections && weekSelections.days) {
                    Object.keys(weekSelections.days).forEach(dayName => {
                        const dayData = weekSelections.days[dayName];
                        if (dayData.enabled) {
                            const weekData = {
                                week: weekOffset + 1,
                                weekStart: new Date(
                                    weekStart), // Create new date object to avoid reference issues
                                day: dayName,
                                start_time: dayData.startTime,
                                end_time: dayData.endTime,
                                additional_slots: dayData.additionalSlots || []
                            };
                            allWeeksData.push(weekData);
                        }
                    });
                }
            }
            return allWeeksData;
        }

        // Save availability functionality
        $(document).ready(function() {
            const $saveBtn = $("#save-availability-btn");
            $saveBtn.on("click", function() {
                // First validate all time inputs
                if (!validateAllTimeInputs()) {
                    return; // Don't proceed if validation fails
                }
                const availabilityData = collectAvailabilityData();
                if (validateAvailabilityData(availabilityData)) {
                    // Determine if we're in edit mode or create mode
                    const isEditMode = $('#edit-availability-data').length > 0;
                    if (isEditMode) {
                        // Store data in edit modal hidden inputs
                        $('#edit-availability-data').val(JSON.stringify(availabilityData.days));
                        $('#edit-vacations-data').val(JSON.stringify(availabilityData.vacations));
                        $('#edit-recurring-data').val(JSON.stringify({
                            recurring: availabilityData.recurring,
                            duration: availabilityData.duration,
                            customWeeks: availabilityData.customWeeks
                        }));
                        // Update the edit availability button
                        const $editAvailabilityBtn = $('#edit-availability-btn');
                        if ($editAvailabilityBtn.length) {
                            $editAvailabilityBtn.html(
                                'Availability Set <span><i class="fa-solid fa-check text-success"></i></span>'
                            ).addClass('text-success');
                        }
                    } else {
                        // Store data in create modal hidden inputs
                        $('#availability-data').val(JSON.stringify(availabilityData.days));
                        $('#vacations-data').val(JSON.stringify(availabilityData.vacations));
                        $('#recurring-data').val(JSON.stringify({
                            recurring: availabilityData.recurring,
                            duration: availabilityData.duration,
                            customWeeks: availabilityData.customWeeks
                        }));
                        // Update the create availability button
                        const $availabilityBtn = $('#availability-btn');
                        if ($availabilityBtn.length) {
                            $availabilityBtn.html(
                                'Availability Set <span><i class="fa-solid fa-check text-success"></i></span>'
                            ).addClass('text-success');
                        }
                    }
                    // Close the modal
                    const modal = bootstrap.Modal.getInstance($('#availabilityModal')[0]);
                    modal.hide();
                }
            });

            function collectAvailabilityData() {
                // First save current week selections to ensure we have the latest data
                AvailabilityModal.weekManager.saveCurrentWeekSelections();
                const data = {
                    days: [],
                    vacations: [],
                    recurring: false,
                    duration: null,
                    customWeeks: null
                };
                // Collect recurring settings first
                const $selectedDuration = $("input[name='duration']:checked");
                if ($selectedDuration.length) {
                    data.recurring = true;
                    data.duration = $selectedDuration.val();
                    if (data.duration === 'custom') {
                        data.customWeeks = $("#custom-weeks-input").length ? $("#custom-weeks-input").val() : null;
                    }
                }
                // If recurring is enabled, collect data from all configured weeks
                if (data.recurring) {
                    const weeksToGenerate = calculateWeeks(data);
                    const allWeeksData = collectAllWeeksData(weeksToGenerate);

                    // If no saved weeks data found, collect from current form state
                    if (allWeeksData.length === 0) {
                        // Collect current week data from form as fallback
                        $(".day-item").each(function() {
                            const $dayElement = $(this);
                            const $checkbox = $dayElement.find(".day-toggle");
                            if ($checkbox.length && $checkbox.prop('checked')) {
                                const dayName = $dayElement.attr("data-day");
                                const $startTimeInput = $dayElement.find(".start-time");
                                const $endTimeInput = $dayElement.find(".end-time");

                                const startTime = $startTimeInput.length ? $startTimeInput.val() :
                                    "10:00 AM";
                                const endTime = $endTimeInput.length ? $endTimeInput.val() : "7:00 PM";

                                // Collect additional time slots
                                const additionalSlots = [];
                                $dayElement.find('.additional-time-slot').each(function() {
                                    const $slot = $(this);
                                    const $additionalStart = $slot.find('.additional-start-time');
                                    const $additionalEnd = $slot.find('.additional-end-time');

                                    if ($additionalStart.length && $additionalEnd.length) {
                                        additionalSlots.push({
                                            start_time: $additionalStart.val(),
                                            end_time: $additionalEnd.val()
                                        });
                                    }
                                });

                                // Add to allWeeksData for processing
                                allWeeksData.push({
                                    week: 1,
                                    weekStart: new Date(AvailabilityModal.weekManager
                                        .currentStartDate),
                                    day: dayName,
                                    start_time: startTime,
                                    end_time: endTime,
                                    additional_slots: additionalSlots
                                });
                            }
                        });
                    }

                    // Convert allWeeksData to the format expected by the backend
                    const dayMap = {};
                    allWeeksData.forEach(weekData => {
                        const dayKey = `${weekData.day}_week_${weekData.week}`;
                        dayMap[dayKey] = {
                            day: weekData.day,
                            start_time: weekData.start_time,
                            end_time: weekData.end_time,
                            additional_slots: weekData.additional_slots || [],
                            week: weekData.week,
                            date: weekData.weekStart
                        };
                    });

                    // Convert to array format
                    data.days = Object.values(dayMap);
                } else {
                    // For non-recurring, collect only current week data from form
                    $(".day-item").each(function() {
                        const $dayElement = $(this);
                        const $checkbox = $dayElement.find(".day-toggle");
                        if ($checkbox.length && $checkbox.prop('checked')) {
                            const dayName = $dayElement.attr("data-day");
                            const $startTimeInput = $dayElement.find(".start-time");
                            const $endTimeInput = $dayElement.find(".end-time");

                            const startTime = $startTimeInput.length ? $startTimeInput.val() : "10:00 AM";
                            const endTime = $endTimeInput.length ? $endTimeInput.val() : "7:00 PM";
                            // Collect additional time slots
                            const additionalSlots = [];
                            $dayElement.find('.additional-time-slot').each(function() {
                                const $slot = $(this);
                                const $additionalStart = $slot.find('.additional-start-time');
                                const $additionalEnd = $slot.find('.additional-end-time');

                                if ($additionalStart.length && $additionalEnd.length) {
                                    additionalSlots.push({
                                        start_time: $additionalStart.val(),
                                        end_time: $additionalEnd.val()
                                    });
                                }
                            });
                            data.days.push({
                                day: dayName,
                                start_time: startTime,
                                end_time: endTime,
                                additional_slots: additionalSlots,
                                week: 1
                            });
                        }
                    });
                }
                // Collect vacation dates
                data.vacations = window.getSelectedVacationDates ? window.getSelectedVacationDates() : [];
                return data;
            }

            function validateAvailabilityData(data) {
                if (data.days.length === 0) {
                    alert("Please select at least one day.");
                    return false;
                }
                if (data.recurring && !data.duration) {
                    alert("Please select a duration for recurring availability.");
                    return false;
                }
                if (data.duration === 'custom' && (!data.customWeeks || data.customWeeks < 1)) {
                    alert("Please enter a valid number of weeks.");
                    return false;
                }
                return true;
            }
        });
    </script>
@endpush
