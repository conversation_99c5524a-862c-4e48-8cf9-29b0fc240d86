@forelse ($bookings as $booking)
    <tr>
        <td data-label="Booking ID">{{ $booking->booking_number }}</td>
        <td data-label="Customer Name">{{ $booking->client_name ? $booking->client_name : $booking->customer->name ?? '-' }}</td>
        <td data-label="Service Name">{{ $booking->service->name }}</td>
        <td data-label="Service Type">
            @if($booking->service->category)
                {{ $booking->service->category->name }}
            @else
                -
            @endif
        </td>
        @if(auth()->check() && auth()->user()->hasRole('admin'))
            <td data-label="Provider">{{ $booking->service->user->name ?? '-' }}</td>
        @endif
        @if ($booking->status == 0)
            @if($booking->hasTimePassed())
                <td data-label="Status" class="status ongoing-status">Ongoing</td>
            @else
                <td data-label="Status" class="status upcoming-status">Upcoming</td>
            @endif
        @elseif($booking->status == 1)
            <td data-label="Status" class="status paid-status">Completed</td>
        @elseif($booking->status == 2)
            <td data-label="Status" class="status unpaid-status">Cancelled</td>
        @else
            <td data-label="Status" class="status">-</td>
        @endif
        <td data-label="Date & Time">
            {{ Carbon\Carbon::parse($booking->booking_date)->format('l, d M Y') }}
            -
            {{ Carbon\Carbon::parse($booking->booking_time)->format('h:i A') }}
        </td>
        <td data-label="Amount">${{ $booking->service->price ?? $booking->service->price ?? 0 }}</td>
        <td data-label="Action">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li>
                        <a class="dropdown-item view fs-14 regular"
                           href="{{ route('booking.detail', ["booking_number" => $booking->booking_number, "ids" => $booking->ids]) }}">
                            <i class="bi bi-eye view-icon"></i>
                            View Details
                        </a>
                    </li>
                    @if($booking->status == 0)
                        @if($booking->hasTimePassed())
                            <li>
                                <button class="dropdown-item complete fs-14 regular booking-action"
                                        type="button"
                                        data-booking-id="{{ $booking->id }}"
                                        data-action="complete">
                                    <i class="bi bi-check-circle complete-icon"></i>
                                    Mark as Complete
                                </button>
                            </li>
                        @endif
                        <li>
                            <button class="dropdown-item cancel fs-14 regular booking-action"
                                    type="button"
                                    data-booking-id="{{ $booking->id }}"
                                    data-action="cancel">
                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                Cancel
                            </button>
                        </li>
                    @endif
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="{{ auth()->check() && auth()->user()->hasRole('admin') ? '9' : '8' }}" class="text-center">No bookings found</td>
    </tr>
@endforelse
