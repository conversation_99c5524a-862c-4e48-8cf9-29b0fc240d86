@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Services</h6>
                        <p class="fs-14 sora light-black m-0">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    @if (!auth()->user()->hasRole('admin'))

                        @php
                            // Set default active tab
                            $activeTab = request()->get('tab', 'services');
                        @endphp

                        <!-- Add Buttons (Blade Conditional) -->
                        @if($activeTab === 'services')
                            <div class="add-service">
                                <a href="{{ route('services.create', ['type' => 'individual', 'tab' => 'services']) }}" class="add-btn">
                                    <i class="fa-solid fa-plus me-3"></i> Add Service
                                </a>
                            </div>
                        @elseif($activeTab === 'classes')
                            <div class="add-class">
                                <a href="{{ route('services.create', ['type' => 'group', 'tab' => 'classes']) }}" class="add-btn">
                                    <i class="fa-solid fa-plus me-3"></i> Add Classes
                                </a>
                            </div>
                        @endif

                        <!-- <div class="add-service">
                            <a href="{{ route('services.create', ['type' => 'individual']) }}" class="add-btn">
                                <i class="fa-solid fa-plus me-3"></i> Add Service
                            </a>
                        </div>

                         <div class="add-class">
                            <a href="{{ route('services.create', ['type' => 'individual']) }}" class="add-btn">
                                <i class="fa-solid fa-plus me-3"></i> Add Service
                            </a>
                        </div> -->
                    @endif
                </div>
                <div class="col-md-12">
                    <!-- <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active business-services {{ $activeTab === 'services' ? 'active' : '' }}" id="pills-services-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-services" type="button" role="tab"
                                aria-controls="pills-services" aria-selected="true">
                                Services
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link business-services  {{ $activeTab === 'classes' ? 'active' : '' }}" id="pills-classes-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-classes" type="button" role="tab" aria-controls="pills-classes"
                                aria-selected="true">
                                Classes
                            </button>
                        </li>
                    </ul> -->

                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link business-services {{ $activeTab === 'services' ? 'active' : '' }}"
                            id="pills-services-tab"
                            href="{{ route(Route::currentRouteName(), ['tab' => 'services']) }}"
                            role="tab" aria-selected="{{ $activeTab === 'services' ? 'true' : 'false' }}">
                                Services
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link business-services {{ $activeTab === 'classes' ? 'active' : '' }}"
                            id="pills-classes-tab"
                            href="{{ route(Route::currentRouteName(), ['tab' => 'classes']) }}"
                            role="tab" aria-selected="{{ $activeTab === 'classes' ? 'true' : 'false' }}">
                                Classes
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade {{ $activeTab === 'services' ? 'show active' : '' }}" id="pills-services" role="tabpanel"
                            aria-labelledby="pills-services-tab" tabindex="0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="table-container">
                                        <div class="table_top d-flex gap-4 align-items-center">
                                            <div class="search_box">
                                                <label for="customSearchInput">
                                                    <i class="fas fa-search"></i>
                                                </label>
                                                <input class="search_input search" type="text" id="servicesSearchInput"
                                                    placeholder="Search..." />
                                            </div>
                                            <!-- Select with dots -->
                                            {{-- <div class="dropdown search_box select-box">
                                                <button
                                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span><span class="dot"></span>
                                                        All</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="All" data-color="#4B5563"><span
                                                                class="dot all"></span>
                                                            All</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Ongoing" data-color="#F59E0B"><span
                                                                class="dot ongoing"></span>
                                                            Ongoing</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Upcoming" data-color="#3B82F6"><span
                                                                class="dot upcoming"></span>
                                                            Upcoming</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Complete" data-color="#10B981"><span
                                                                class="dot completed"></span>
                                                            Complete</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Canceled" data-color="#EF4444"><span
                                                                class="dot cancelled-dot"></span>
                                                            Canceled</a></li>
                                                </ul>
                                            </div> --}}
                                            <!-- category -->
                                            <div class="search_box select-box">
                                                <select class="search_input category-filter">
                                                    <option value="all">All Categories</option>
                                                    @foreach($categories as $category)
                                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <table id="responsiveTable"
                                            class="display wallet-history-table w-100">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th>Category</th>
                                                    <th>Duration</th>
                                                    <th>Price</th>
                                                    <th>Assigned Staff</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody id="servicesTableBody">
                                                @include('dashboard.service.partials.services-table')
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade {{ $activeTab === 'classes' ? 'show active' : '' }}" id="pills-classes" role="tabpanel"
                            aria-labelledby="pills-classes-tab" tabindex="0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="table-container">
                                        <div class="table_top d-flex gap-4 align-items-center">
                                            <div class="search_box">
                                                <label for="customSearchInput">
                                                    <i class="fas fa-search"></i>
                                                </label>
                                                <input class="search_input search" type="text" id="classesSearchInput"
                                                    placeholder="Search..." />
                                            </div>

                                            <!-- Select with dots -->
                                            {{-- <div class="dropdown search_box select-box">
                                                <button
                                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <span><span class="dot"></span>
                                                        All</span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="All" data-color="#4B5563"><span
                                                                class="dot all"></span>
                                                            All</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Ongoing" data-color="#F59E0B"><span
                                                                class="dot ongoing"></span>
                                                            Ongoing</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Upcoming" data-color="#3B82F6"><span
                                                                class="dot upcoming"></span>
                                                            Upcoming</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Complete" data-color="#10B981"><span
                                                                class="dot completed"></span>
                                                            Complete</a></li>
                                                    <li><a class="dropdown-item dropdown-status" href="#"
                                                            data-label="Canceled" data-color="#EF4444"><span
                                                                class="dot cancelled-dot"></span>
                                                            Canceled</a></li>
                                                </ul>
                                            </div> --}}
                                            <!-- category -->
                                            <div class="search_box select-box">
                                                <select class="search_input category-filter">
                                                    <option value="all">All Categories</option>
                                                    @foreach($categories as $category)
                                                        <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <table id="responsiveTable" class=" display wallet-history-table w-100 ">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th>Category</th>
                                                    <th>Timmings</th>
                                                    <th>Days</th>
                                                    <th> Slots</th>
                                                    <th>Price Per Slots</th>
                                                    <th>Assigned Staff</th>
                                                    <th></th>
                                                </tr>
                                            </thead>
                                            <tbody id="classesTableBody">
                                                @include('dashboard.service.partials.classes-table')
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    let currentTab = '{{ $activeTab }}';

    // Update current tab when tab is clicked
    $(document).on('click', '.nav-link', function() {
        const href = $(this).attr('href');
        if (href && href.includes('tab=')) {
            const urlParams = new URLSearchParams(href.split('?')[1]);
            currentTab = urlParams.get('tab') || 'services';
        }
    });

    // Function to filter services
    function filterServices() {
        let search = '';
        let category = '';

        if (currentTab === 'classes') {
            search = $('#classesSearchInput').val().trim();
            category = $('#pills-classes .category-filter').val();
        } else {
            search = $('#servicesSearchInput').val().trim();
            category = $('#pills-services .category-filter').val();
        }

        // Prepare data object
        let data = {
            tab: currentTab
        };

        if (search) {
            data.search = search;
        }

        if (category && category !== 'all') {
            data.category = category;
        }

        console.log('Services filter data:', data);

        // Show loading state
        showLoadingState();

        $.ajax({
            url: "{{ route('services.filter') }}",
            type: "GET",
            data: data,
            beforeSend: function() {
                // Add searching class to search inputs
                $('.search').addClass('searching');
            },
            success: function(response) {
                if (response.success) {
                    // Update appropriate table content
                    if (currentTab === 'classes') {
                        $('#classesTableBody').html(response.html);
                    } else {
                        $('#servicesTableBody').html(response.html);
                    }
                } else {
                    console.error('Filter failed:', response.message);
                }

                hideLoadingState();
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                hideLoadingState();
            },
            complete: function() {
                // Remove searching class
                $('.search').removeClass('searching');
            }
        });
    }

    // Search input event handlers
    $(document).on('input', '.search', function() {
        clearTimeout(window.searchTimeout);
        window.searchTimeout = setTimeout(function() {
            filterServices();
        }, 500); // Debounce search
    });

    // Category filter change handler
    $(document).on('change', '.category-filter', function() {
        filterServices();
    });

    // Loading state functions
    function showLoadingState() {
        if (currentTab === 'classes') {
            $('#classesTableBody').html('<tr><td colspan="8" class="text-center py-5"><i class="fas fa-spinner fa-spin fa-2x"></i><br>Loading...</td></tr>');
        } else {
            $('#servicesTableBody').html('<tr><td colspan="6" class="text-center py-5"><i class="fas fa-spinner fa-spin fa-2x"></i><br>Loading...</td></tr>');
        }
    }

    function hideLoadingState() {
        // Loading state is replaced by actual content in success callback
    }
});
</script>
@endpush
