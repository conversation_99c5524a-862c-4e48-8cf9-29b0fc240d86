<?php $__env->startPush('css'); ?>
    <!--begin::Vendor Stylesheets(used for this page only)-->
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/custom/fullcalendar/fullcalendar.bundle.css" rel="stylesheet"
        type="text/css" />
    <!--end::Vendor Stylesheets-->
    <style>
        /* FullCalendar specific styles to override master layout conflicts */
        #booking-calendar {
            font-family: 'Sora', sans-serif;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        #booking-calendar .fc {
            font-family: 'Sora', sans-serif;
        }

        #booking-calendar .fc-timegrid-slot {
            height: 60px !important;
            border: 1px solid #e5e7eb !important;
        }

        #booking-calendar .fc-timegrid-col {
            min-width: 60px !important;
        }

        #booking-calendar .fc-timegrid-axis {
            width: 80px !important;
            background: #f9fafb;
            border-right: 2px solid #e5e7eb !important;
        }

        #booking-calendar .fc-timegrid-axis-cushion {
            padding: 8px 12px;
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        #booking-calendar .fc-col-header-cell {
            background: #f9fafb;
            border: 1px solid #e5e7eb !important;
            padding: 12px 8px;
            font-weight: 600;
            color: #374151;
        }

        #booking-calendar .fc-daygrid-day,
        #booking-calendar .fc-timegrid-col {
            border: 1px solid #e5e7eb !important;
        }

        #booking-calendar .fc-event {
            border-radius: 6px !important;
            border: none !important;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: 500;
            margin: 1px;
        }

        #booking-calendar .fc-event-title {
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        #booking-calendar .fc-timegrid-event {
            border-radius: 4px !important;
            margin: 1px 2px;
        }

        #booking-calendar .fc-now-indicator-line {
            border-color: #ef4444;
            border-width: 2px;
        }

        #booking-calendar .fc-now-indicator-arrow {
            border-left-color: #ef4444;
            border-width: 6px;
        }

        /* Ensure proper table layout */
        #booking-calendar table {
            width: 100% !important;
            table-layout: fixed !important;
        }

        #booking-calendar .fc-scrollgrid {
            border: 1px solid #e5e7eb !important;
            border-radius: 8px;
            overflow: hidden;
        }

        #booking-calendar .fc-scrollgrid-section>* {
            border-left: 0 !important;
            border-right: 0 !important;
        }

        #booking-calendar .fc-scrollgrid-section-header>td {
            border-bottom: 2px solid #e5e7eb !important;
        }

        /* Time slot styling */
        #booking-calendar .fc-timegrid-slot-minor {
            border-top: 1px dashed #e5e7eb !important;
        }

        #booking-calendar .fc-timegrid-slot-major {
            border-top: 1px solid #d1d5db !important;
        }

        /* Today column highlighting */
        #booking-calendar .fc-day-today {
            background-color: #fef3c7 !important;
        }

        .business-booking-table .table-container{
            overflow-y: scroll;
            overflow-x: hidden;
            height: 500px;
        }
        

        /* Responsive adjustments */
        @media (max-width: 768px) {
            #booking-calendar .fc-timegrid-axis {
                width: 60px !important;
            }

            #booking-calendar .fc-event {
                font-size: 11px;
            }
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid booking">
        <div id="kt_app_content_container" class="app-container container padding-block booking-section">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="semi_bold sora black">Bookings</h6>
                        <p class="fs-14 normal sora light-black">Lorem ipsum dolor sit amet consectetur. </p>
                    </div>
                    <?php if(auth()->user()->hasAnyRole(['individual', 'business']) && auth()->user()->services->isNotEmpty()): ?>
                        
                        <button class="blue-button add-to-cart-btn" data-bs-toggle="modal"
                            data-bs-target="#service-details">Add Booking</button>
                    <?php endif; ?>
                </div>
            </div>
            <div class="row row-gap-5 mb-10 card-wrapper">
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-blue">
                                    <?php echo $__env->make('svg.customer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Total Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($stats['total_bookings'] ?? 0); ?>">
                                </p>
                            </div>
                            <div class="card-footer ">
                                <div
                                    class="fs-12 w-700 <?php echo e($stats['total_bookings_change'] >= 0 ? 'green-box green' : 'red-box red'); ?>">
                                    <i
                                        class="fa-solid fa-arrow-<?php echo e($stats['total_bookings_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow'); ?>"></i>
                                    <?php echo e(abs($stats['total_bookings_change'])); ?>%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-purple">
                                    <?php echo $__env->make('svg.professional', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Completed Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($stats['active_bookings'] ?? 0); ?>">
                                </p>
                            </div>
                            <div class="card-footer">
                                <div
                                    class="fs-12 w-700 <?php echo e($stats['active_bookings_change'] >= 0 ? 'green-box green' : 'red-box red'); ?>">
                                    <i
                                        class="fa-solid fa-arrow-<?php echo e($stats['active_bookings_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow'); ?>"></i>
                                    <?php echo e(abs($stats['active_bookings_change'])); ?>%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-orange">
                                    <?php echo $__env->make('svg.ongoing', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Ongoing Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($stats['ongoing_bookings'] ?? 0); ?>">
                                </p>
                            </div>
                            <div class="card-footer">
                                <div
                                    class="fs-12 w-700 <?php echo e($stats['ongoing_bookings_change'] >= 0 ? 'green-box green' : 'red-box red'); ?>">
                                    <i
                                        class="fa-solid fa-arrow-<?php echo e($stats['ongoing_bookings_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow'); ?>"></i>
                                    <?php echo e(abs($stats['ongoing_bookings_change'])); ?>%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
                <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                    <a href="javascript:void(0)">
                        <div class=" card-box d-flex flex-row gap-3 align-items-center">
                            <div class="card-header">
                                <div class="icon_cards bg-light-green">
                                    <?php echo $__env->make('svg.user', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                            <div class="card-body w-150px ">
                                <p class="fs-14 opacity-6 light-black m-0">
                                    Upcoming Bookings
                                </p>
                                <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                    data-kt-countup-value="<?php echo e($stats['upcoming_bookings'] ?? 0); ?>">
                                </p>
                            </div>
                            <div class="card-footer">
                                <div
                                    class="fs-12 w-700 <?php echo e($stats['upcoming_bookings_change'] >= 0 ? 'green-box green' : 'red-box red'); ?>">
                                    <i
                                        class="fa-solid fa-arrow-<?php echo e($stats['upcoming_bookings_change'] >= 0 ? 'up analytics-green-arrow' : 'down analytics-red-arrow'); ?>"></i>
                                    <?php echo e(abs($stats['upcoming_bookings_change'])); ?>%
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="row business-booking-table">
                <div class="col-lg-12">
                    <?php if(auth()->check() && auth()->user()->hasAnyRole('individual', 'business')): ?>
                        <div class="table-container">
                            <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                <ul class="ms-auto booking-tabs nav nav-pills mb-3 justify-content-end" id="view-tab"
                                    role="tablist">
                                    <li class="nav-item " role="presentation">
                                        <button class="nav-link active calendar-view " id="list-tab" data-bs-toggle="pill"
                                            data-bs-target="#list-view" type="button" role="tab"
                                            aria-controls="list-view" aria-selected="true">
                                            <i class="fa-solid fa-list me-2"></i> List View
                                        </button>
                                    </li>
                                    <li class="nav-item " role="presentation">
                                        <button class="nav-link calendar-view" id="calendar-tab" data-bs-toggle="pill"
                                            data-bs-target="#calendar-view" type="button" role="tab"
                                            aria-controls="calendar-view" aria-selected="false">
                                            <i class="fa-regular fa-calendar me-2"></i> Calendar View
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <div class="tab-content" id="view-tabContent">
                                <div class="tab-pane fade show active" id="list-view" role="tabpanel"
                                    aria-labelledby="list-tab" tabindex="0">
                                    <?php echo $__env->make('dashboard.business.partials.booking-search-filters', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                    <?php echo $__env->make('dashboard.business.partials.booking-table', [
                                        'bookings' => $bookings,
                                    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                                <div class="tab-pane fade" id="calendar-view" role="tabpanel" aria-labelledby="calendar-tab"
                                    tabindex="0">
                                    <!-- 📅 Calendar View Content -->
                                    <div class="row">

                                        <div class="col-lg-12  p-0">
                                            <div class="schedule-container ">
                                                <div class="calendar-header flex-align-space-btw">
                                                    <div class="flex-align-space-btw mb-10">
                                                        <div class="calendar-controls d-flex  gap-2 align-items-center">
                                                            <div class="d-flex gap-4">
                                                                <p id="today"
                                                                    class="m-0 fs-13 regular black cursor-pointer">Today
                                                                </p>
                                                                <button id="prev-week" class="btn-prev"><i
                                                                        class="fa-solid fa-chevron-left"></i></button>
                                                                <button id="next-week" class="btn-next"><i
                                                                        class="fa-solid fa-chevron-right"></i></button>
                                                            </div>
                                                            <h3 id="date-range" class="m-0 fs-16 semi-bold black">
                                                                Loading...
                                                            </h3>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="booking-calendar"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if(auth()->check() && auth()->user()->hasRole('admin')): ?>
                        <div class="table-container">
                            <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                <?php echo $__env->make('dashboard.business.partials.booking-search-filters', [
                                    'showExport' => true,
                                ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <?php echo $__env->make('dashboard.business.partials.booking-table', [
                                'bookings' => $bookings,
                            ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <?php echo $__env->make('dashboard.templates.modal.professional.add-booking-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>



<?php $__env->startPush('js'); ?>
    <!--begin::Vendor Javascripts(used for this page only)-->
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js"></script>
    <!--end::Vendor Javascripts-->
    <script>
        $(document).ready(function() {
            let searchTimeout;
            let currentStatus = 'all';
            let currentCategory = 'Category';
            let currentStaff = 'Staff';
            let currentDate = '';

            // Initialize date picker
            //  if ($('.datePicker').length) {
            //       $('.datePicker').flatpickr({
            //        mode: "range",
            //      dateFormat: "M d, Y",
            //    onChange: function(selectedDates, dateStr, instance) {
            //        currentDate = dateStr;
            //        performSearch();
            //      }
            //    });
            //   }

            // Search input with debouncing
            $('#customSearchInput').on('keyup', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    performSearch();
                }, 300);
            });

            // Status dropdown handling
            $('.dropdown-status').on('click', function(e) {
                e.preventDefault();
                const label = $(this).data('label');
                const color = $(this).data('color');

                // Update button text and color
                const button = $(this).closest('.dropdown').find('.status-dropdown-button span');
                button.html(`<span class="dot" style="background-color: ${color}"></span> ${label}`);

                // Set current status
                currentStatus = label.toLowerCase();
                performSearch();
            });

            // Category dropdown handling
            $('select').on('change', function() {
                const selectName = $(this).find('option:first').val();
                const selectedValue = $(this).val();

                if (selectName === 'Category') {
                    currentCategory = selectedValue;
                } else if (selectName === 'Staff') {
                    currentStaff = selectedValue;
                }

                performSearch();
            });

            // Booking action handlers (Complete/Cancel)
            $(document).on('click', '.booking-action', function(e) {
                e.preventDefault();
                const bookingId = $(this).data('booking-id');
                const action = $(this).data('action');

                // Show SweetAlert confirmation
                const actionText = action === 'complete' ? 'complete' : 'cancel';
                const actionColor = action === 'complete' ? '#10B981' : '#EF4444';
                const actionIcon = action === 'complete' ? 'success' : 'warning';

                Swal.fire({
                    title: `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Booking?`,
                    text: `Are you sure you want to ${actionText} this booking?`,
                    icon: actionIcon,
                    showCancelButton: true,
                    confirmButtonColor: actionColor,
                    cancelButtonColor: '#6B7280',
                    confirmButtonText: `Yes, ${actionText} it!`,
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        updateBookingStatus(bookingId, action);
                    }
                });
            });

            function performSearch() {
                const searchQuery = $('#customSearchInput').val();

                // Show loading state
                $('#bookingTableBody').html('<tr><td colspan="8" class="text-center">Loading...</td></tr>');

                $.ajax({
                    url: '<?php echo e(route('booking.filter')); ?>',
                    type: 'GET',
                    data: {
                        search: searchQuery,
                        status: currentStatus,
                        category: currentCategory,
                        staff: currentStaff,
                        date: currentDate
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#bookingTableBody').html(response.html);

                            // Show result count
                            if (searchQuery || currentStatus !== 'all' || currentCategory !==
                                'Category' || currentStaff !== 'Staff' || currentDate) {
                                console.log(`Found ${response.count} booking(s)`);
                            }
                        } else {
                            $('#bookingTableBody').html(
                                '<tr><td colspan="8" class="text-center">Error loading bookings</td></tr>'
                            );
                        }
                    },
                    error: function() {
                        $('#bookingTableBody').html(
                            '<tr><td colspan="8" class="text-center">Error loading bookings</td></tr>'
                        );
                    }
                });
            }

            function updateBookingStatus(bookingId, action) {
                // Show loading
                Swal.fire({
                    title: 'Processing...',
                    text: 'Updating booking status',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '<?php echo e(route('booking.update-status')); ?>',
                    type: 'POST',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>',
                        booking_id: bookingId,
                        action: action
                    },
                    success: function(response) {
                        if (response.success) {
                            // Show success message
                            const actionText = action === 'complete' ? 'completed' : 'cancelled';
                            Swal.fire({
                                title: 'Success!',
                                text: `Booking ${actionText} successfully!`,
                                icon: 'success',
                                confirmButtonColor: '#006AA0',
                                timer: 2000,
                                timerProgressBar: true,
                                willClose: () => {
                                    // Reload the page after success message
                                    window.location.reload();
                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Failed to update booking status',
                                icon: 'error',
                                confirmButtonColor: '#EF4444'
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Network error occurred while updating booking status',
                            icon: 'error',
                            confirmButtonColor: '#EF4444'
                        });
                    }
                });
            }

            // Helper function to get status color
            function getStatusColor(status) {
                switch (status) {
                    case 0:
                        return '#F59E0B'; // Orange for ongoing/pending
                    case 1:
                        return '#10B981'; // Green for completed
                    case 2:
                        return '#EF4444'; // Red for cancelled
                    default:
                        return '#6B7280'; // Gray for unknown
                }
            }

            // Calendar functionality
            let calendar;
            let calendarInitialized = false;

            // Initialize calendar when calendar tab is shown
            $('#calendar-tab').on('shown.bs.tab', function(e) {
                if (!calendarInitialized) {
                    initializeCalendar();
                    calendarInitialized = true;
                } else if (calendar) {
                    // Refresh events if calendar already exists
                    calendar.refetchEvents();
                }
            });

            // Also initialize calendar if calendar tab is already active on page load
            if ($('#calendar-tab').hasClass('active')) {
                setTimeout(function() {
                    if (!calendarInitialized) {
                        initializeCalendar();
                        calendarInitialized = true;
                    }
                }, 100);
            }

            function initializeCalendar() {
                const calendarEl = document.getElementById('booking-calendar');
                if (!calendarEl) return;

                // Destroy existing calendar if it exists
                if (calendar) {
                    calendar.destroy();
                }

                calendar = new FullCalendar.Calendar(calendarEl, {
                    headerToolbar: false, // We use custom header
                    initialView: 'timeGridWeek',
                    height: 'auto',
                    slotMinTime: '08:00:00',
                    slotMaxTime: '20:00:00',
                    slotDuration: '00:30:00',
                    allDaySlot: false,
                    nowIndicator: true,
                    editable: false,
                    selectable: false,
                    selectMirror: false,
                    dayMaxEvents: true,
                    weekends: true,
                    eventDidMount: function(info) {
                        // Ensure unique events by checking if event already exists
                        console.log('Event mounted:', info.event.id, info.event.title);
                    },
                    events: {
                        url: '<?php echo e(route('booking.calendar-data')); ?>',
                        method: 'GET',
                        failure: function() {
                            alert('There was an error while fetching booking events!');
                        },
                        success: function(data) {
                            console.log('Calendar events loaded:', data.length);
                        }
                    },
                    eventClick: function(info) {
                        // Show booking details in SweetAlert
                        const props = info.event.extendedProps;
                        const statusColor = getStatusColor(props.status);

                        Swal.fire({
                            title: `<strong>${props.service_name}</strong>`,
                            html: `
                        <div class="booking-details" style="text-align: left; font-size: 14px;">
                            <div style="margin-bottom: 15px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                    <span style="font-weight: 600; color: #374151;">Booking ID:</span>
                                    <span style="color: #6b7280;">${props.booking_number}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                    <span style="font-weight: 600; color: #374151;">Customer:</span>
                                    <span style="color: #6b7280;">${props.customer_name}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                    <span style="font-weight: 600; color: #374151;">Date:</span>
                                    <span style="color: #6b7280;">${props.booking_date}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                    <span style="font-weight: 600; color: #374151;">Time:</span>
                                    <span style="color: #6b7280;">${props.booking_time}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                    <span style="font-weight: 600; color: #374151;">Duration:</span>
                                    <span style="color: #6b7280;">${props.duration} minutes</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                    <span style="font-weight: 600; color: #374151;">Amount:</span>
                                    <span style="color: #059669; font-weight: 600;">$${props.service_price}</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                    <span style="font-weight: 600; color: #374151;">Status:</span>
                                    <span style="color: ${statusColor}; font-weight: 600; padding: 2px 8px; border-radius: 12px; background: ${statusColor}20;">${props.status_text}</span>
                                </div>
                                ${props.comments ? `
                                        <div style="margin-top: 12px; padding-top: 12px; border-top: 1px solid #e5e7eb;">
                                            <span style="font-weight: 600; color: #374151;">Comments:</span>
                                            <p style="margin: 4px 0 0 0; color: #6b7280; font-style: italic;">${props.comments}</p>
                                        </div>
                                        ` : ''}
                            </div>
                        </div>
                    `,
                            icon: 'info',
                            confirmButtonText: 'Close',
                            confirmButtonColor: '#006AA0',
                            width: '500px',
                            customClass: {
                                popup: 'booking-details-popup'
                            }
                        });
                    }
                });

                calendar.render();
                updateCalendarHeader();
            }

            function updateCalendarHeader() {
                if (!calendar) return;

                const currentDate = calendar.getDate();
                const view = calendar.view;

                // Update date range display
                const startDate = view.currentStart;
                const endDate = new Date(view.currentEnd);
                endDate.setDate(endDate.getDate() - 1); // Adjust end date

                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };
                const startStr = startDate.toLocaleDateString('en-US', options);
                const endStr = endDate.toLocaleDateString('en-US', options);

                $('#date-range').text(`${startStr} - ${endStr}`);
            }

            // Calendar navigation
            $('#prev-week').on('click', function() {
                if (calendar) {
                    calendar.prev();
                    updateCalendarHeader();
                }
            });

            $('#next-week').on('click', function() {
                if (calendar) {
                    calendar.next();
                    updateCalendarHeader();
                }
            });

            $('#today').on('click', function() {
                if (calendar) {
                    calendar.today();
                    updateCalendarHeader();
                }
            });

            // Handle export button click
            $(document).on('click', '#exportBookingsBtn', function(e) {
                e.preventDefault();

                // Show loading state
                const originalText = $(this).html();
                $(this).html(
                    '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Exporting...'
                    );

                // Trigger download
                window.location.href = '<?php echo e(route('booking.export')); ?>';

                // Reset button after a delay
                setTimeout(function() {
                    $('#exportBookingsBtn').html(originalText);
                }, 2000);
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\SALMAN\git\anders\resources\views/dashboard/business/business-booking.blade.php ENDPATH**/ ?>